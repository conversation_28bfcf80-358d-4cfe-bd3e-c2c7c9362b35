const path = require('path');
const chalk = require('chalk');
const fs = require('fs-extra');

/**
 * BuildErrorAnalyzer - 构建错误分析器（重构版）
 *
 * 专门负责：
 * - 构建错误输出分析
 * - 错误类型识别和分类
 * - 文件路径有效性验证
 * - 错误哈希生成和去重
 * - 与PromptBuilder协作生成分析提示词
 * - AI分析响应解析
 * - 统一的错误输出处理（构建错误、运行时错误、开发服务器错误）
 * - 错误格式化和转换
 * - 错误模式检测和过滤
 */
class BuildErrorAnalyzer {
  constructor(projectPath, aiService, toolExecutor, options = {}) {
    this.projectPath = projectPath;
    this.aiService = aiService;
    this.toolExecutor = toolExecutor;
    this.options = {
      verbose: false,
      ...options
    };

    this.promptBuilder = null;
    this.dependencyMapper = null; // 将由 BuildFixAgent 设置
  }

  /**
   * 设置PromptBuilder实例
   */
  setPromptBuilder(promptBuilder) {
    this.promptBuilder = promptBuilder;
  }

  /**
   * 设置DependencyMapper实例
   */
  setDependencyMapper(dependencyMapper) {
    this.dependencyMapper = dependencyMapper;
  }

  /**
   * 分析构建错误并选择需要修复的文件
   */
  async analyzeBuildErrors(buildOutput, attemptNumber = 1) {
    if (!this.aiService.isEnabled()) {
      throw new Error('AI 服务不可用，无法进行错误分析');
    }

    try {
      if (this.options.verbose) {
        console.log(chalk.gray(`🔍 构建错误输出长度: ${buildOutput.length} 字符`));
      }

      const deduplicatedOutput = this.deduplicateErrors(buildOutput);
      const suggestions = await this.generateErrorSuggestions(deduplicatedOutput);
      let prompt = await this.generateAnalysisPrompt(deduplicatedOutput, suggestions);

      const response = await this.aiService.callAI(prompt, {
        context: {
          taskType: 'error-analysis',
          attemptNumber: attemptNumber,
          phase: 'analysis',
          buildOutputLength: deduplicatedOutput.length,
          suggestionsCount: suggestions.length
        }
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`🤖 AI 响应长度: ${response.length} 字符`));
      }

      const filesToFix = this.parseAnalysisResponse(response);

      if (filesToFix.length === 0) {
        throw new Error('AI 未能识别需要修复的文件');
      }

      console.log(chalk.gray(`✅ AI 识别出 ${filesToFix.length} 个需要修复的文件`));
      filesToFix.forEach(file => {
        console.log(chalk.gray(`  - ${file}`));
      });

      return {
        success: true,
        filesToFix,
        suggestions,
        processedOutput: deduplicatedOutput
      };
    } catch (error) {
      console.log(chalk.red(`❌ AI 分析异常: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  async generateAnalysisPrompt(buildOutput) {
    const maxOutputLength = 5000;
    const truncatedOutput = buildOutput.length > maxOutputLength
      ? buildOutput.substring(0, maxOutputLength) + '\n... (输出已截断)'
      : buildOutput;

    const suggestionsText = await this.generateErrorSuggestions(buildOutput);

    return `你是一个专业的 Vue 2 到 Vue 3 迁移专家和构建错误分析师。请分析以下构建错误输出，并确定需要修复的文件。

**任务目标**：
1. 分析构建错误输出
2. 识别导致错误的具体文件
3. 返回需要修复的文件路径列表

**构建错误输出**：
\`\`\`
${truncatedOutput}
\`\`\`
**错误处理建议（FAQ）**：
${suggestionsText}

**工具可用**：
你可以使用以下工具来帮助分析：
${this.toolExecutor.getToolsDescription()}

**响应格式**：
请使用以下 XML 格式返回分析结果：

\`\`\`xml
<analysis>
<files_to_fix>
<file>src/components/Example.vue</file>
<file>src/utils/helper.js</file>
</files_to_fix>
<reasoning>
简要说明为什么选择这些文件进行修复
</reasoning>
</analysis>
\`\`\`

请仔细分析错误信息，重点关注：
- 文件路径和行号信息
- 模块导入错误
- Vue 2/3 兼容性问题
- TypeScript 类型错误
- 依赖包问题
- Webpack 配置错误

**重要约束**：
- 只返回项目源代码文件，不要包含 node_modules 中的文件
- 对于 Webpack 配置错误，应该检查 vue.config.js、webpack.config.js 等配置文件
- 对于插件错误，重点关注项目配置而非第三方库内部文件
- 文件路径应该相对于项目根目录
- 参考上面的修复建议，优先处理有明确解决方案的错误

只返回确实需要修改代码的文件，不要包含 node_modules 或系统文件。`;
  }

  /**
   * 解析 AI 分析响应
   */
  parseAnalysisResponse(response) {
    try {
      const xmlMatch = response.match(/<analysis>[\s\S]*?<files_to_fix>([\s\S]*?)<\/files_to_fix>[\s\S]*?<\/analysis>/);

      if (xmlMatch) {
        const filesSection = xmlMatch[1];
        const fileMatches = filesSection.match(/<file>(.*?)<\/file>/g);

        if (fileMatches) {
          return fileMatches.map(match => {
            let file = match.replace(/<\/?file>/g, '').trim();
            if (file.startsWith(this.projectPath)) {
              file = path.relative(this.projectPath, file);
            }

            if (file.startsWith('/src')) {
              file = file.substring(1); // 去掉 /
            }

            if (file.startsWith('./src')) {
              file = file.substring(2); // 去掉 ./
            }

            return file;
          }).filter(file => this.isValidProjectFile(file));
        }
      }

      const lines = response.split('\n');
      const files = [];

      for (const line of lines) {
        const fileMatch = line.match(/(?:src\/|\.\/)?[\w/\-.]+\.(vue|js|ts|jsx|tsx)$/);
        if (fileMatch) {
          files.push(fileMatch[0]);
        }
      }

      return [...new Set(files)].filter(file => this.isValidProjectFile(file));
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 分析响应失败，使用空列表'));
      return [];
    }
  }

  /**
   * 验证文件是否为有效的项目文件
   */
  isValidProjectFile(filePath) {
    if (!filePath || typeof filePath !== 'string') {
      return false;
    }

    if (filePath.includes('node_modules')) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`  ⚠️  跳过 node_modules 文件: ${filePath}`));
      }
      return false;
    }

    if (path.isAbsolute(filePath) && !filePath.startsWith(this.projectPath)) {
      if (this.options.verbose) {
        console.log(chalk.yellow(`  ⚠️  跳过系统文件: ${filePath}`));
      }
      return false;
    }

    const allowedExtensions = ['.vue', '.js', '.ts', '.jsx', '.tsx', '.json', '.scss', '.css'];
    const allowedConfigFiles = ['vue.config.js', 'webpack.config.js', 'vite.config.js', 'vite.config.ts'];

    const ext = path.extname(filePath);
    const fileName = path.basename(filePath);

    if (allowedExtensions.includes(ext) || allowedConfigFiles.includes(fileName)) {
      return true;
    }

    if (this.options.verbose) {
      console.log(chalk.yellow(`  ⚠️  跳过不支持的文件类型: ${filePath}`));
    }
    return false;
  }

  /**
   * 去除重复的构建错误信息
   */
  deduplicateErrors(buildOutput) {
    if (!buildOutput || typeof buildOutput !== 'string') {
      return buildOutput;
    }

    const lines = buildOutput.split('\n');
    const seenErrors = new Set();
    const deduplicatedLines = [];
    let consecutiveEmptyLines = 0;

    for (const line of lines) {
      const trimmedLine = line.trim();

      if (this.shouldSkipLine(trimmedLine)) {
        continue;
      }

      if (trimmedLine === '') {
        consecutiveEmptyLines++;
        if (consecutiveEmptyLines <= 1) {
          deduplicatedLines.push('');
        }
        continue;
      } else {
        consecutiveEmptyLines = 0;
      }

      const normalizedLine = this.normalizeErrorLine(trimmedLine);
      if (this.isErrorOrWarningLine(trimmedLine) && seenErrors.has(normalizedLine)) {
        continue;
      }

      if (this.isErrorOrWarningLine(trimmedLine)) {
        seenErrors.add(normalizedLine);
      }

      const simplifiedLine = this.simplifyPaths(trimmedLine);
      deduplicatedLines.push(simplifiedLine);
    }

    while (deduplicatedLines.length > 0 && deduplicatedLines[deduplicatedLines.length - 1].trim() === '') {
      deduplicatedLines.pop();
    }

    const originalLength = lines.length;
    const deduplicatedLength = deduplicatedLines.length;

    if (this.options.verbose && originalLength !== deduplicatedLength) {
      console.log(chalk.gray(`🔄 错误去重: ${originalLength} → ${deduplicatedLength} 行 (减少 ${originalLength - deduplicatedLength} 行重复)`));
    }

    return deduplicatedLines.join('\n');
  }

  shouldSkipLine(line) {
    if (!line || line.trim() === '') {
      return false;
    }

    // 跳过调试器信息
    if (line.includes('Debugger listening on') ||
        line.includes('Debugger attached') ||
        line.includes('::v-deep usage as a combinator has been deprecated') ||
        line.includes('For help, see: https://nodejs.org/en/docs/inspector')) {
      return true;
    }

    // 跳过重复的构建进度信息
    if (line.match(/^[\s-]*Building.*for production/)) {
      return true;
    }

    // 跳过 ELIFECYCLE 信息，但保留包含具体错误信息的行
    if (line.includes('ELIFECYCLE') && line.includes('Command failed') &&
        !line.includes('TypeError') && !line.includes('SyntaxError') &&
        !line.includes('ReferenceError') && !line.includes('content')) {
      return true;
    }

    // 跳过过长的 Generated code 行
    if (line.includes('Generated code for') && line.length > 200) {
      return true;
    }

    // 跳过重复的 inner error 分隔符
    if (line.trim() === '-- inner error --') {
      return true;
    }

    // 跳过过多的连续相同字符（如 ===== 或 ----- ）
    if (line.match(/^[\s]*[=-]{10,}[\s]*$/)) {
      return true;
    }

    return false;
  }

  /**
   * 简化路径信息，移除冗长的 node_modules 路径
   */
  simplifyPaths(line) {
    if (!line || typeof line !== 'string') {
      return line;
    }

    let simplified = line.replace(
      /\/[^\/\s]*\/node_modules\/\.pnpm\/[^\/\s]*\/node_modules\//g,
      '/node_modules/'
    );

    // 简化普通 node_modules 路径
    simplified = simplified.replace(
      /\/[^\/\s]*\/node_modules\//g,
      '/node_modules/'
    );

    // 简化 webpack loader 路径
    simplified = simplified.replace(
      /\?\?clonedRuleSet-\d+\.use\[\d+\]!/g,
      '??loader!'
    );

    return simplified;
  }

  /**
   * 判断是否为错误或警告行
   */
  isErrorOrWarningLine(line) {
    if (!line || typeof line !== 'string') {
      return false;
    }

    // 主要错误模式
    const errorPatterns = [
      /\berror\b/i,
      /\bwarning\b/i,
      /\bfailed\b/i,
      /syntax error/i,
      /undefined variable/i,
      /module not found/i,
      /can't resolve/i,
      /validationerror/i,
      /syntaxerror/i,
      /referenceerror/i,
      /typeerror/i,
      /hookwebpackerror/i,
      /\[@vue\/compiler-sfc\]/i,
      /build failed/i,
      /compilation error/i
    ];

    // 检查是否匹配错误模式
    const hasErrorPattern = errorPatterns.some(pattern => pattern.test(line));

    // 排除非错误的包含关键词的行
    const excludePatterns = [
      /debugger/i,
      /listening on/i,
      /for help, see:/i,
      /attached/i,
      /building.*for production/i
    ];

    const shouldExclude = excludePatterns.some(pattern => pattern.test(line));

    return hasErrorPattern && !shouldExclude;
  }

  /**
   * 标准化错误行，去除变化的部分
   */
  normalizeErrorLine(line) {
    return line
      // 去除时间戳
      .replace(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/g, '[TIMESTAMP]')
      // 去除具体的文件路径，保留相对路径结构
      .replace(/\/[^\s]+\/([^\/\s]+\.(vue|js|ts|jsx|tsx|scss|css))/g, '[PATH]/$1')
      // 去除行号和列号
      .replace(/:\d+:\d+/g, ':LINE:COL')
      // 去除 webpack 构建进度符号
      .replace(/[⠋⠙⠹⠸⠼⠴⠦⠧⠇⠏]/g, '')
      // 标准化空白字符
      .replace(/\s+/g, ' ')
      .trim();
  }

  /**
   * 生成构建错误的常见问题建议数组
   */
  async generateErrorSuggestionsArray(buildOutput) {
    const suggestions = [];
    const lines = buildOutput.split('\n');

    for (const line of lines) {
      const lowerLine = line.toLowerCase();

      // SCSS 变量未定义错误
      if (lowerLine.includes('syntax error: undefined variable') ||
          lowerLine.includes('undefined variable')) {
        const variableMatch = line.match(/\$[\w-]+/);
        if (variableMatch) {
          suggestions.push({
            type: 'scss_undefined_variable',
            error: line.trim(),
            suggestion: `SCSS 变量 ${variableMatch[0]} 未定义。建议：            
检查是否有 variables.scss 或类似的变量文件，然后使用 @use 'variables.scss';
`,
            tools: ['list_files', 'read_file'],
            searchPattern: '**/*variable*.scss'
          });
        }
      }

      if (lowerLine.includes('cannot read properties of null')) {
        suggestions.push({
          type: 'type_error',
          error: line.trim(),
          suggestion: `Vue 2 的函数式组件语法在 Vue 3 中已改变
**解决方案：**
\`\`\`javascript
// Vue 2 写法 (错误)
export default {
  props: { /* ... */ },
  renderContent(h, { node, data }) {
    return <div>{data.title}</div>
  }
}

// Vue 3 写法 (正确)
import { h } from 'vue' // 需要引入
export default {
  props: { /* ... */ },
  renderContent({ node, data }) {
    return h('div', null, data.title)
  }
}
\`\`\`

`,
          tools: ['list_files', 'read_file'],
          searchPattern: '**/*.vue'
        });
      }

      // 大小写敏感路径错误（CaseSensitivePathsPlugin）
      if (lowerLine.includes('casesensitivepathsplugin') ||
          lowerLine.includes('does not match the corresponding path on disk')) {
        // 提取错误路径信息
        const pathMatch = line.match(/`([^`]+)`.*corresponding path on disk `([^`]+)`/) ||
                         line.match(/'([^']+)'.*corresponding path on disk '([^']+)'/) ||
                         line.match(/"([^"]+)".*corresponding path on disk "([^"]+)"/);

        if (pathMatch) {
          const expectedPath = pathMatch[1];
          const actualPath = pathMatch[2];

          // 提取目录路径和文件名
          const expectedDir = expectedPath.substring(0, expectedPath.lastIndexOf('/'));
          const actualFileName = actualPath;

          // 直接列出目录中的所有文件
          let directoryListing = '';
          try {
            const fullDirPath = path.resolve(this.projectPath, expectedDir);
            if (await fs.pathExists(fullDirPath)) {
              const files = await fs.readdir(fullDirPath);
              directoryListing = `\n\n**目录 \`${expectedDir}\` 中的实际文件:**\n${files.map(f => `- ${f}`).join('\n')}`;
            }
          } catch (error) {
            directoryListing = `\n\n**无法读取目录 \`${expectedDir}\`**: ${error.message}`;
          }

          suggestions.push({
            type: 'case_sensitive_path',
            error: line.trim(),
            expectedPath: expectedPath,
            actualPath: actualPath,
            suggestion: `**大小写敏感路径错误**
**问题**: 导入路径 \`${expectedPath}\` 与磁盘上的实际文件名 \`${actualFileName}\` 大小写不匹配。

**解决方案**:
1. **重命名文件**: 将 \`${actualFileName}\` 重命名为匹配导入路径的大小写
2. **修改导入语句**: 将导入路径改为匹配实际文件名的大小写

**推荐重命名操作**:
- 找到实际文件名，使用 rename_file 工具重命名为: \`${expectedPath.substring(expectedPath.lastIndexOf('/') + 1)}\``,
            tools: ['rename_file'],
            targetDirectory: expectedDir,
            searchPattern: '**/*.vue,**/*.js,**/*.ts',
            renameOperation: {
              sourcePath: actualPath,
              targetPath: expectedPath.substring(expectedPath.lastIndexOf('/') + 1)
            }
          });
        }
      }

      // 模块未找到错误
      if (lowerLine.includes('module not found') || lowerLine.includes("can't resolve")) {
        // 改进的模块名提取正则表达式
        const moduleMatch = line.match(/Can't resolve '([^']+)'/) ||
                           line.match(/Can't resolve "([^"]+)"/) ||
                           line.match(/Module not found.*'([^']+)'/) ||
                           line.match(/Module not found.*"([^"]+)"/);
        if (moduleMatch) {
          const moduleName = moduleMatch[1];

          // 检查是否有第三方库映射
          let migrationInfo = null;
          if (this.dependencyMapper && this.dependencyMapper.hasMapping(moduleName)) {
            migrationInfo = this.dependencyMapper.getMigrationInfo(moduleName);
          }

          let suggestion = `模块 '${moduleName}' 未找到。`;

          /// load package.json file
          const packageJsonPath = path.join(this.projectPath, 'package.json');
          let packageJson;
          if (fs.existsSync(packageJsonPath)) {
            packageJson = fs.readJsonSync(packageJsonPath);
          }

          if (migrationInfo) {
            const difficultyEmoji = {
              'easy': '🟢',
              'medium': '🟡',
              'hard': '🔴'
            };

            const typeEmoji = {
              'package_replacement': '📦',
              'component_architecture_change': '🏗️',
              'version_upgrade': '⬆️',
              'data_format_change': '🔄'
            };

            suggestion = `**第三方库迁移**: '${moduleName}' 需要迁移到 '${migrationInfo.target}'

**迁移信息**:
- 难度: ${difficultyEmoji[migrationInfo.difficulty] || '⚪'} ${migrationInfo.difficulty || 'unknown'}
- 类型: ${typeEmoji[migrationInfo.migrationType] || '📋'} ${migrationInfo.migrationType || 'unknown'}
- Vue 3 支持: ${migrationInfo.vue3Support ? '✅' : '❌'}

**迁移步骤**:`;

            // 使用文档中的安装命令（如果有）
            if (migrationInfo.installCommands) {
              suggestion += `
1. ${migrationInfo.installCommands.remove || `npm uninstall ${moduleName}`}
2. ${migrationInfo.installCommands.add || `npm install ${migrationInfo.target}`}`;
            } else {
              suggestion += `
1. 卸载旧包: \`npm uninstall ${moduleName}\`
2. 安装新包: \`npm install ${migrationInfo.target}\``;
            }

            suggestion += `
3. 更新导入语句: \`import ... from '${migrationInfo.target}'\`
4. 根据新 API 调整组件使用方式`;

            // 添加破坏性变更警告
            if (migrationInfo.breakingChanges && migrationInfo.breakingChanges.length > 0) {
              suggestion += `

**⚠️ 破坏性变更** (${migrationInfo.breakingChanges.length} 项):`;
              migrationInfo.breakingChanges.slice(0, 3).forEach((change, index) => {
                suggestion += `\n${index + 1}. ${change}`;
              });
              if (migrationInfo.breakingChanges.length > 3) {
                suggestion += `\n... 还有 ${migrationInfo.breakingChanges.length - 3} 项变更`;
              }
            }

            // 添加 API 变更信息
            if (migrationInfo.apiChanges && migrationInfo.apiChanges.length > 0) {
              suggestion += `

**🔧 主要 API 变更** (${migrationInfo.apiChanges.length} 项):`;
              migrationInfo.apiChanges.slice(0, 2).forEach((change, index) => {
                if (typeof change === 'object') {
                  suggestion += `\n${index + 1}. ${change.type}: ${change.from} → ${change.to}`;
                } else {
                  suggestion += `\n${index + 1}. ${change}`;
                }
              });
              if (migrationInfo.apiChanges.length > 2) {
                suggestion += `\n... 还有 ${migrationInfo.apiChanges.length - 2} 项变更`;
              }
            }

            suggestion += `

**详细文档**: 查看 ${migrationInfo.docPath}`;

            // 添加外部链接
            if (migrationInfo.targetInfo && migrationInfo.targetInfo.github) {
              suggestion += `
**GitHub**: ${migrationInfo.targetInfo.github}`;
            }
            if (migrationInfo.targetInfo && migrationInfo.targetInfo.npm) {
              suggestion += `
**NPM**: ${migrationInfo.targetInfo.npm}`;
            }
          } else {
            suggestion += `
建议：
1. 检查模块是否已安装在 package.json 中
2. 检查导入路径是否正确
3. 对于第三方组件，查看是否需要 Vue 3 兼容版本
4. 尝试寻找 Vue 3 兼容的替代方案

该项目的 package.json 信息如下：
${packageJson ? JSON.stringify(packageJson, null, 2) : '未找到 package.json 文件'}
`;
          }

          suggestions.push({
            type: migrationInfo ? 'third_party_migration' : 'module_not_found',
            error: line.trim(),
            module: moduleName,
            migrationInfo: migrationInfo,
            suggestion: suggestion,
            tools: ['read_file', 'list_files'],
            checkComponentMappings: true
          });
        }
      }

      // TypeScript 类型错误
      if (lowerLine.includes('ts(') || lowerLine.includes('typescript error')) {
        suggestions.push({
          type: 'typescript_error',
          error: line.trim(),
          suggestion: `TypeScript 类型错误。建议：
1. 检查类型定义是否正确
2. 确认导入的类型是否存在
3. 检查 Vue 3 相关的类型定义是否已更新
4. 考虑添加类型断言或更新类型定义`,
          tools: ['read_file', 'str_replace']
        });
      }

      // Webpack 配置错误
      if (lowerLine.includes('configuration') && (lowerLine.includes('error') || lowerLine.includes('invalid'))) {
        suggestions.push({
          type: 'webpack_config_error',
          error: line.trim(),
          suggestion: `Webpack 配置错误。建议：
1. 检查 vue.config.js 或 webpack.config.js 配置
2. 确认 Vue 3 相关的 loader 配置是否正确
3. 检查插件版本是否与 Vue 3 兼容
4. 参考 Vue 3 官方迁移指南更新配置`,
          tools: ['read_file', 'list_files'],
          configFiles: ['vue.config.js', 'webpack.config.js', 'vite.config.js']
        });
      }

      // v-model cannot be used on a prop, because local prop bindings are not writable
      if (lowerLine.includes('v-model cannot be used on a prop')) {
        suggestions.push({
          type: 'v_model_error',
          error: 'v-model cannot be used on a prop',
          suggestion: `v-model 不能直接用于 prop，因为 prop 是单向只读的。可以改为 ref 方式 const dialogVisible = ref(false)，或者请将其拆分为 :model-value 绑定和 @update:model-value 事件监听，通过 emit 通知父组件更新。`,
          tools: ['read_file', 'str_replace'],
          searchPattern: '**/*.vue'
        });
      }

      // v-model cannot be used on a prop, because local prop bindings are not writable
      if (lowerLine.includes('cannot read properties of undefined (reading \'type\')')) {
        suggestions.push({
          type: '<template> use error',
          error: 'TypeError: Cannot read properties of undefined (reading \'type\')',
          suggestion: `\`v-slot\` (或其简写 \`#\`) 是一个插槽指令，它只能用于 **Vue 组件**上，或用于包裹内容的 \`<template>\` 标签（当其父级是组件时）。
当 \`v-slot\` 被错误地应用在 \`<div>\` 这样的原生 HTML 元素上时，Vue 的渲染引擎无法正确解析模板结构，导致内部的虚拟节点(VNode)对象为 \`undefined\`，
从而在读取其 \`type\` 属性时发生错误。`,
          tools: ['read_file', 'str_replace'],
          searchPattern: '**/*.vue'
        });
      }
    }

    // 去重建议
    const uniqueSuggestions = this.deduplicateSuggestions(suggestions);

    if (uniqueSuggestions.length > 0 && this.options.verbose) {
      console.log(chalk.gray(`💡 生成了 ${uniqueSuggestions.length} 个错误修复建议`));
    }

    return uniqueSuggestions;
  }

  /**
   * 生成构建错误的常见问题建议
   * 直接返回格式化后的提示词文本
   */
  async generateErrorSuggestions(buildOutput) {
    const suggestions = await this.generateErrorSuggestionsArray(buildOutput);
    return JSON.stringify(suggestions);
  }

  /**
   * 去重建议
   */
  deduplicateSuggestions(suggestions) {
    const seen = new Set();
    return suggestions.filter(suggestion => {
      const key = `${suggestion.type}:${suggestion.error}`;
      if (seen.has(key)) {
        return false;
      }
      seen.add(key);
      return true;
    });
  }

  /**
   * 生成错误哈希
   */
  generateErrorHash(buildOutput) {
    const errorLines = buildOutput.split('\n').filter(line => {
      const lowerLine = line.toLowerCase();
      return lowerLine.includes('error') ||
             lowerLine.includes('failed') ||
             lowerLine.includes('validationerror') ||
             lowerLine.includes('syntaxerror') ||
             lowerLine.includes('referenceerror');
    });

    const normalizedErrors = errorLines.map(line => {
      return line
        .replace(/\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2}/g, '[TIMESTAMP]')
        .replace(/\/[^\s]+\//g, '[PATH]/')
        .replace(/\s+/g, ' ')
        .trim();
    });

    return normalizedErrors.join('|');
  }

  /**
   * 统一的错误输出处理入口
   * 支持不同类型的错误输出：build、runtime、dev-server
   */
  async processBuildOutput(output, type = 'build', options = {}) {
    if (!output || typeof output !== 'string') {
      return {
        success: false,
        error: 'Invalid output provided',
        processedOutput: '',
        errorLines: [],
        errorCount: 0
      };
    }

    try {
      // 第一步：去重错误信息
      const deduplicatedOutput = this.deduplicateErrors(output);

      // 第二步：提取错误行
      const errorLines = this.extractErrorLines(deduplicatedOutput, type);

      // 第三步：检测错误模式
      const detectedPatterns = this.detectErrorPatterns(deduplicatedOutput, type);

      // 第四步：分类错误
      const categorizedErrors = this.categorizeErrors(errorLines, type);

      return {
        success: true,
        processedOutput: deduplicatedOutput,
        originalOutput: output,
        errorLines,
        errorCount: errorLines.length,
        detectedPatterns,
        categorizedErrors,
        type,
        suggestions: await this.generateErrorSuggestionsArray(deduplicatedOutput)
      };
    } catch (error) {
      return {
        success: false,
        error: error.message,
        processedOutput: output,
        errorLines: [],
        errorCount: 0
      };
    }
  }

  /**
   * 将运行时错误格式化为构建输出格式
   */
  formatRuntimeErrorAsBuildOutput(errorContext) {
    const {
      fileName,
      message,
      stack,
      componentTrace,
      route,
      errors,
      timestamp,
      lineNumber,
      count = 1,
      // PageValidator 特有的字段
      suggestedFiles,
      routeComponentInfo,
      routeInfo
    } = errorContext;

    let output = '';

    // 构建错误头部信息
    if (route) {
      output += `页面运行时错误报告\n`;
      output += `==================\n`;
      output += `页面路径: ${route.path || 'Unknown'}\n`;
      output += `页面名称: ${route.name || 'Unknown'}\n`;
    } else {
      output += `运行时错误报告\n`;
      output += `==============\n`;
    }

    output += `错误时间: ${timestamp || new Date().toISOString()}\n`;

    if (fileName) {
      output += `错误文件: ${fileName}\n`;
      if (lineNumber) {
        output += `错误位置: ${fileName}:${lineNumber}\n`;
      }
    }

    if (count > 1) {
      output += `错误次数: ${count}\n`;
    }

    output += `\n`;

    // 路由配置信息（PageValidator 特有）
    if (routeInfo) {
      output += `路由配置信息:\n`;
      output += `${routeInfo}\n\n`;
    }

    // 主要错误信息
    if (message) {
      output += `错误信息:\n`;
      output += `Error: ${message}\n\n`;
    } else if (errors && Array.isArray(errors)) {
      // 处理多个错误的情况
      const errorMessages = errors.map(error => {
        if (typeof error === 'string') {
          return error;
        } else {
          return JSON.stringify(error);
        }
      });

      output += `错误详情:\n`;
      output += `${errorMessages.map((msg, index) => `${index + 1}. ${msg}`).join('\n')}\n\n`;
    }

    // 堆栈跟踪
    if (stack) {
      output += `堆栈跟踪:\n`;
      output += `${stack}\n\n`;
    }

    // Vue 组件跟踪
    if (componentTrace && componentTrace.length > 0) {
      output += `Vue 组件跟踪:\n`;
      componentTrace.forEach((trace, index) => {
        output += `  ${index + 1}. ${trace.name || 'Anonymous'} (${trace.file}:${trace.line})\n`;
      });
      output += `\n`;
    }

    // 组件文件映射（PageValidator 特有）
    if (routeComponentInfo) {
      output += `组件文件映射:\n`;
      output += `${routeComponentInfo.directMapping ? `直接映射: ${routeComponentInfo.directMapping}` : '无直接映射'}\n`;
      if (routeComponentInfo.relatedMappings && routeComponentInfo.relatedMappings.length > 0) {
        output += `相关路由映射:\n${routeComponentInfo.relatedMappings.map(m => `  ${m.route} -> ${m.component}`).join('\n')}\n`;
      } else {
        output += `无相关路由映射\n`;
      }
      output += `\n`;
    }

    // 推荐检查的文件（PageValidator 特有）
    if (suggestedFiles && suggestedFiles.length > 0) {
      output += `推荐检查的文件:\n`;
      output += `${suggestedFiles.map((file, index) => `${index + 1}. ${file}`).join('\n')}\n\n`;
    }

    // 错误分析（PageValidator 特有）
    if (route) {
      output += `错误分析:\n`;
      output += `- 页面访问URL: ${route.path}\n`;
      output += `- 错误类型: 运行时错误\n`;
      output += `- 可能原因: Vue组件错误（Vue 2使用的组件在 Vue 3 没有对应的版本）、JavaScript语法错误、依赖问题、路由配置错误等\n`;
      if (suggestedFiles && suggestedFiles.length > 0) {
        output += `- 优先检查文件: ${suggestedFiles.slice(0, 3).join(', ')}\n`;
      } else {
        output += `- 优先检查文件: 根据错误信息确定\n`;
      }
      output += `\n`;
    }

    // 添加调试信息（如果需要）
    if (this.options.verbose && errors) {
      output += `原始错误对象:\n`;
      output += `${JSON.stringify(errors, null, 2)}\n`;
    }

    return output;
  }

  /**
   * 提取错误行（支持不同类型的输出）
   */
  extractErrorLines(output, type = 'build') {
    if (!output || typeof output !== 'string') {
      return [];
    }

    const lines = output.split('\n');
    const errorLines = [];

    for (const line of lines) {
      if (this.isErrorLine(line, type)) {
        errorLines.push(line.trim());
      }
    }

    return errorLines;
  }

  /**
   * 判断是否为错误行（支持不同类型）
   */
  isErrorLine(line, type = 'build') {
    if (!line || typeof line !== 'string') {
      return false;
    }

    const lowerLine = line.toLowerCase();

    // 通用错误模式
    const commonErrorPatterns = [
      /error/i,
      /failed/i,
      /exception/i,
      /uncaught/i
    ];

    // 根据类型添加特定模式
    let specificPatterns = [];

    switch (type) {
      case 'build':
        specificPatterns = [
          /validationerror/i,
          /syntaxerror/i,
          /referenceerror/i,
          /typeerror/i,
          /cannot read propert/i,
          /module not found/i,
          /cannot find module/i,
          /compilation error/i,
          /build error/i,
          /\[@vue\/compiler-sfc\]/i
        ];
        break;

      case 'runtime':
        specificPatterns = [
          /cannot read propert/i,
          /is not defined/i,
          /is not a function/i,
          /vue.*error/i,
          /component.*error/i,
          /render.*error/i,
          /computed.*error/i,
          /watch.*error/i,
          /lifecycle.*error/i
        ];
        break;

      case 'dev-server':
        specificPatterns = [
          /webpack/i,
          /compilation/i,
          /hot.*reload/i,
          /dev.*server/i
        ];
        break;
    }

    const allPatterns = [...commonErrorPatterns, ...specificPatterns];
    return allPatterns.some(pattern => pattern.test(lowerLine));
  }

  /**
   * 检测错误模式（支持不同类型）
   */
  detectErrorPatterns(output, type = 'build') {
    if (!output || typeof output !== 'string') {
      return [];
    }

    const detectedPatterns = [];
    const lines = output.split('\n');

    // 定义不同类型的错误模式
    const patternDefinitions = {
      build: [
        { pattern: /module not found/i, type: 'module_not_found', severity: 'error' },
        { pattern: /syntax error/i, type: 'syntax_error', severity: 'error' },
        { pattern: /typeerror/i, type: 'type_error', severity: 'error' },
        { pattern: /cannot read propert/i, type: 'property_access_error', severity: 'error' },
        { pattern: /reading 'content'/i, type: 'content_access_error', severity: 'error' },
        { pattern: /::v-deep usage as a combinator has been deprecated/i, type: 'vue_deep_selector', severity: 'warning' },
        { pattern: /undefined variable/i, type: 'scss_undefined_variable', severity: 'error' },
        { pattern: /typescript error/i, type: 'typescript_error', severity: 'error' },
        { pattern: /webpack.*configuration/i, type: 'webpack_config_error', severity: 'error' }
      ],
      runtime: [
        { pattern: /cannot read propert/i, type: 'property_access_error', severity: 'error' },
        { pattern: /is not defined/i, type: 'undefined_variable', severity: 'error' },
        { pattern: /is not a function/i, type: 'function_call_error', severity: 'error' },
        { pattern: /vue.*error/i, type: 'vue_error', severity: 'error' },
        { pattern: /component.*error/i, type: 'component_error', severity: 'error' }
      ],
      'dev-server': [
        { pattern: /webpack.*error/i, type: 'webpack_error', severity: 'error' },
        { pattern: /compilation.*failed/i, type: 'compilation_failed', severity: 'error' },
        { pattern: /hot.*reload.*failed/i, type: 'hot_reload_failed', severity: 'warning' }
      ]
    };

    const patterns = patternDefinitions[type] || patternDefinitions.build;

    for (const line of lines) {
      for (const patternDef of patterns) {
        if (patternDef.pattern.test(line)) {
          detectedPatterns.push({
            type: patternDef.type,
            severity: patternDef.severity,
            line: line.trim(),
            pattern: patternDef.pattern.source
          });
        }
      }
    }

    return detectedPatterns;
  }

  /**
   * 分类错误（支持不同类型）
   */
  categorizeErrors(errorLines, type = 'build') {
    if (!Array.isArray(errorLines)) {
      return {
        critical: [],
        fixable: [],
        warnings: [],
        unknown: []
      };
    }

    const categorized = {
      critical: [],
      fixable: [],
      warnings: [],
      unknown: []
    };

    // 定义不同类型的分类规则
    const categorizationRules = {
      build: {
        critical: [
          /syntax error/i,
          /module not found/i,
          /cannot find module/i,
          /compilation.*failed/i,
          /typeerror/i,
          /cannot read propert/i,
          /reading 'content'/i
        ],
        fixable: [
          /undefined variable/i,
          /::v-deep/i,
          /typescript error/i,
          /vue.*error/i
        ],
        warnings: [
          /deprecated/i,
          /warning/i
        ]
      },
      runtime: {
        critical: [
          /cannot read propert/i,
          /is not defined/i,
          /is not a function/i
        ],
        fixable: [
          /vue.*error/i,
          /component.*error/i,
          /render.*error/i
        ],
        warnings: [
          /warning/i,
          /deprecated/i
        ]
      }
    };

    const rules = categorizationRules[type] || categorizationRules.build;

    for (const errorLine of errorLines) {
      let categorized_flag = false;

      // 检查是否为关键错误
      for (const pattern of rules.critical) {
        if (pattern.test(errorLine)) {
          categorized.critical.push(errorLine);
          categorized_flag = true;
          break;
        }
      }

      if (categorized_flag) continue;

      // 检查是否为可修复错误
      for (const pattern of rules.fixable) {
        if (pattern.test(errorLine)) {
          categorized.fixable.push(errorLine);
          categorized_flag = true;
          break;
        }
      }

      if (categorized_flag) continue;

      // 检查是否为警告
      for (const pattern of rules.warnings) {
        if (pattern.test(errorLine)) {
          categorized.warnings.push(errorLine);
          categorized_flag = true;
          break;
        }
      }

      if (!categorized_flag) {
        categorized.unknown.push(errorLine);
      }
    }

    return categorized;
  }

  /**
   * 检查文本中是否包含错误（从 BuildExecutor 迁移）
   */
  containsErrors(text) {
    if (!text || typeof text !== 'string') {
      return false;
    }

    const errorPatterns = [
      /error/i,
      /ERROR/,
      /failed/i,
      /FAILED/,
      /cannot find module/i,
      /module not found/i,
      /syntax error/i,
      /type error/i,
      /compilation error/i,
      /build error/i
    ];

    return errorPatterns.some(pattern => pattern.test(text));
  }

  /**
   * 处理构建错误（从 BuildExecutor 迁移）
   */
  async handleBuildError(error, options = {}) {
    const errorOutput = (error.stdout || '') + (error.stderr || '');
    const processedResult = await this.processBuildOutput(errorOutput, 'build', options);

    // 显示错误摘要
    if (errorOutput.trim()) {
      console.log(chalk.red('\n构建错误信息:'));

      const displayCount = options.verbose ?
        Math.min(20, processedResult.errorLines.length) :
        Math.min(10, processedResult.errorLines.length);

      processedResult.errorLines.slice(0, displayCount).forEach(line => {
        console.log(chalk.gray(`  ${line}`));
      });

      if (processedResult.errorLines.length > displayCount) {
        const remainingCount = processedResult.errorLines.length - displayCount;
        if (options.verbose) {
          console.log(chalk.gray(`  ... 以及 ${remainingCount} 个其他错误`));
        } else {
          console.log(chalk.gray(`  ... 以及 ${remainingCount} 个其他错误 (使用 --verbose 查看详情)`));
        }
      }

      if (options.verbose) {
        console.log(chalk.gray('\n完整错误输出:'));
        console.log(chalk.red(errorOutput));
      }
    } else {
      console.log(chalk.yellow('⚠️ 无法获取具体错误信息'));
    }

    return {
      success: false,
      output: errorOutput,
      error: error.message || '构建失败',
      processedResult
    };
  }
}

module.exports = BuildErrorAnalyzer;
