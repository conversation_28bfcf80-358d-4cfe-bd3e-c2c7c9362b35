const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../../ai/AiService');
const ToolExecutor = require('../../ai/tools/ToolExecutor');
const PromptBuilder = require('../../ai/prompts/PromptRequestBuilder');
const BuildErrorAnalyzer = require('./BuildErrorAnalyzer');
const PromptResponseHandler = require('../../ai/prompts/PromptResponseHandler');
const VueFileValidator = require('../../frameworks/vue/utils/VueFileValidator');
const StringReplaceTool = require('../../ai/tools/StringReplaceTool');
const ComponentDependencyMapper = require('../../frameworks/vue/third-party/ComponentDependencyMapper');
const FileFixHistoryManager = require('./FileFixHistoryManager');

class BuildFixAgent extends AIService {
  constructor(projectPath, options = {}) {
    const aiOptions = {
      ...options,
      logDir: options.logDir || path.join(projectPath || process.cwd(), 'logs')  // 使用统一日志目录
    };
    super(aiOptions);

    this.projectPath = projectPath;
    this.options = {
      maxAttempts: 10,
      dryRun: false,
      verbose: false,
      ...options
    };

    // 初始化重构后的工具执行器
    this.toolExecutor = new ToolExecutor(projectPath, this.options);

    // 初始化提示词构建器
    this.promptBuilder = new PromptBuilder(this.toolExecutor.getToolRegistry(), this.options);

    // 初始化响应处理器
    this.responseHandler = new PromptResponseHandler(this.options);

    // 初始化字符串替换工具
    this.stringReplaceTool = new StringReplaceTool(this.options);

    // 初始化第三方库依赖映射器
    this.dependencyMapper = new ComponentDependencyMapper();

    // 初始化文件修复历史管理器
    this.historyManager = new FileFixHistoryManager(projectPath, {
      ...this.options,
      historyDir: path.join(this.options.logDir || path.join(projectPath, 'logs'), 'fix-history'),
      verbose: this.options.verbose
    });

    // 初始化错误分析器
    this.errorAnalyzer = new BuildErrorAnalyzer(projectPath, this, this.toolExecutor, this.options);

    // 设置ErrorAnalyzer的PromptBuilder和DependencyMapper
    this.errorAnalyzer.setPromptBuilder(this.promptBuilder);
    this.errorAnalyzer.setDependencyMapper(this.dependencyMapper);

    // 重复检测
    this.attemptHistory = {
      filesToFix: [], // 记录每次尝试修复的文件列表
      errorHashes: [], // 记录每次的错误哈希
      lastErrorOutput: null // 记录上次的错误输出
    };

    // 修复统计
    this.fixStats = {
      filesAnalyzed: 0,
      filesModified: 0,
      errorsFixed: 0,
      attempts: 0
    };
  }

  async fixFirstFile(files, buildOutput, attemptNumber = 1, suggestions = []) {
    let filesModified = 0;
    const errors = [];

    /**
     * 由于错误的 stack 是存在调用链，可能会将正确的文件也包含在内，模型在判断的时候容易出错，进而将正确的文件改错。
     */
    let fileToFix = [files[0]]

    const previousAttempts = this.getPreviousAttempts(fileToFix, attemptNumber);

    for (let fileIndex = 0; fileIndex < fileToFix.length; fileIndex++) {
      let filePath = fileToFix[fileIndex];
      // check filePath is absolute path
      if (!path.isAbsolute(filePath)) {
        filePath = path.resolve(this.projectPath, filePath);
      }

      try {
        console.log(chalk.gray(`🔧 修复文件: ${filePath}`));
        const fileResult = await this.toolExecutor.executeToolCall('read_file', { file_path: filePath });

        if (!fileResult.success) {
          console.log(chalk.yellow(`  ⚠️  无法读取文件: ${fileResult.error}`));
          errors.push(`无法读取文件 ${filePath}: ${fileResult.error}`);
          continue;
        }

        // 跳过长文件
        if (fileResult.content.length > this.options.maxFileSize) {
          console.log(chalk.yellow(`  ⚠️  文件过大： ${fileResult.content.length} 字节，跳过`));
          errors.push(`文件过大 ${filePath}: ${fileResult.content.length} 字节，跳过`);
          continue;
        }

        const filePreviousAttempts = previousAttempts.filter(attempt => attempt.filePath === filePath);
        let relevantSuggestions = []
        if (suggestions && suggestions.length > 0) {
          relevantSuggestions = suggestions
        } else {
          relevantSuggestions = await this.errorAnalyzer.generateErrorSuggestions(buildOutput, filePath)
        }

        const fixResult = await this.fixSingleFile(
          filePath,
          fileResult.content,
          buildOutput,
          attemptNumber,
          fileIndex + 1,
          fileToFix.length,
          filePreviousAttempts,
          relevantSuggestions
        );

        if (fixResult.success) {
          if (fixResult.method === 'str_replace') {
            console.log(chalk.green('  ✅ 文件修复成功 (str_replace)'));
            filesModified++;
            this.fixStats.filesModified++;
            continue;
          }

          // 确保内容是字符串类型
          let contentToWrite = fixResult.newContent || fixResult.fixedContent || fixResult.aiResponse;

          if (typeof contentToWrite !== 'string') {
            console.log(chalk.yellow(`  ⚠️  内容类型错误: ${typeof contentToWrite}, 期望: string`));
            errors.push(`内容类型错误 ${filePath}: 期望字符串，得到 ${typeof contentToWrite}`);
            continue;
          }

          const writeResult = await this.toolExecutor.executeToolCall('write_file', {
            file_path: filePath,
            content: contentToWrite
          });

          if (writeResult.success) {
            console.log(chalk.green('  ✅ 文件修复成功'));
            filesModified++;
            this.fixStats.filesModified++;
          } else {
            console.log(chalk.yellow(`  ⚠️  无法写入文件: ${writeResult.error}`));
            errors.push(`无法写入文件 ${filePath}: ${writeResult.error}`);
          }
        } else {
          console.log(chalk.yellow(`  ⚠️  AI 修复失败: ${fixResult.error}`));

          // 显示 AI 响应的部分内容用于调试
          if (fixResult.aiResponse) {
            console.log(chalk.gray(`     AI 响应预览: ${fixResult.aiResponse.substring(0, 200)}...`));
          }

          // 如果是解析错误，显示更多调试信息
          if (fixResult.error.includes('无法解析AI响应格式')) {
            console.log(chalk.gray(`     🔍 调试信息: 响应长度 ${fixResult.aiResponse ? fixResult.aiResponse.length : 0} 字符`));
            // 尝试提取部分响应内容用于调试
            if (fixResult.aiResponse) {
              const responseSnippet = fixResult.aiResponse.substring(0, 500);
              console.log(chalk.gray(`     📝 响应片段: ${responseSnippet}...`));
              // 检查是否缺少闭合标签
              if (fixResult.aiResponse.includes('<fix_result>') && !fixResult.aiResponse.includes('</fix_result>')) {
                console.log(chalk.yellow(`     ⚠️  检测到未闭合的 <fix_result> 标签`));
              }
              if (fixResult.aiResponse.includes('<fixed_content>') && !fixResult.aiResponse.includes('</fixed_content>')) {
                console.log(chalk.yellow(`     ⚠️  检测到未闭合 <fixed_content> 标签`));
              }
            }
            if (this.options.verbose && fixResult.aiResponse) {
              console.log(chalk.gray(`     📝 完整响应内容:`));
              console.log(chalk.gray(fixResult.aiResponse));
            }
          }

          errors.push(`AI 修复失败 ${filePath}: ${fixResult.error}`);

          // 记录失败的尝试
          this.recordFailedAttempt(filePath, attemptNumber, fixResult.error);
        }
      } catch (error) {
        console.log(chalk.red(`  ❌ 修复文件异常: ${error.message}`));
        errors.push(`修复文件异常 ${filePath}: ${error.message}`);
        this.recordFailedAttempt(filePath, attemptNumber, error.message);
      }
    }

    this.fixStats.attempts++;
    this.fixStats.filesAnalyzed += fileToFix.length;

    return {
      success: filesModified > 0,
      filesModified,
      errors: errors.length > 0 ? errors : undefined,
      totalFiles: fileToFix.length
    };
  }

  /**
   * 修复单个文件 - 两轮AI调用模式
   * 第一轮：根据错误生成工具调用，决定需要读取哪些文件
   * 第二轮：基于读取的文件内容和错误信息，生成具体的修复代码
   */
  async fixSingleFile(filePath, fileContent, buildOutput, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = [], suggestions = []) {
    let fixContext = null;

    try {
      // 开始文件修复尝试，获取历史信息
      fixContext = await this.historyManager.startFileFixAttempt(filePath, buildOutput, attemptNumber, {
        fileIndex,
        totalFiles,
        suggestions
      });

      if (this.options.verbose) {
        console.log(chalk.gray(`📝 文件修复历史: ${fixContext.fileHistory.length} 次尝试, ${fixContext.previousFailures.length} 次失败`));
        if (fixContext.suggestedStrategies.length > 0) {
          console.log(chalk.gray(`💡 推荐策略: ${fixContext.suggestedStrategies.map(s => s.strategy).join(', ')}`));
        }
      }

      // 合并历史信息到 previousAttempts
      const enrichedPreviousAttempts = [
        ...previousAttempts,
        ...fixContext.previousFailures
      ];

      const toolCalls = await this.generateToolCalls(filePath, buildOutput, attemptNumber, enrichedPreviousAttempts, suggestions);

      let contextFiles = {};
      if (toolCalls && toolCalls.length > 0) {
        contextFiles = await this.executeToolCalls(toolCalls);
      }

      const result = await this.generateFileFix(
        filePath, fileContent, buildOutput, contextFiles,
        attemptNumber, fileIndex, totalFiles, enrichedPreviousAttempts, suggestions, fixContext.attemptId
      );

      // 结束修复尝试并记录结果
      await this.historyManager.endFileFixAttempt(
        fixContext.attemptId,
        result.success,
        result,
        result.error
      );

      return result;
    } catch (error) {
      console.error(chalk.red(`修复文件失败: ${filePath}`), error.message);

      return {
        success: false,
        error: error.message,
        filePath
      };
    }
  }

  /**
   * 修复运行时错误
   * 专门处理Vue运行时错误，包括组件错误、响应式数据问题等
   */
  async fixRuntimeError(errorContext) {
    try {
      const { fileName, message, stack, componentTrace, buildOutput } = errorContext;

      if (this.options.verbose) {
        console.log(chalk.gray(`🔧 开始修复运行时错误: ${fileName}`));
      }

      // 读取出错文件内容
      const filePath = path.resolve(this.projectPath, fileName);
      if (!await fs.pathExists(filePath)) {
        throw new Error(`文件不存在: ${fileName}`);
      }

      const fileContent = await fs.readFile(filePath, 'utf8');

      // 使用专门的运行时错误修复流程
      const fixResult = await this.fixRuntimeErrorFile(
        fileName, fileContent, errorContext
      );

      if (this.options.verbose) {
        console.log(chalk.gray(`       🔍 修复结果: success=${fixResult.success}, hasNewContent=${!!fixResult.newContent}, hasFixedContent=${!!fixResult.fixedContent}`));
      }

      // 检查修复结果
      const fixedContent = fixResult.newContent || fixResult.fixedContent;

      if (fixResult.success && fixedContent) {
        // 应用修复
        if (!this.options.dryRun) {
          await fs.writeFile(filePath, fixedContent, 'utf8');
          console.log(chalk.green(`✅ 运行时错误修复已应用: ${fileName}`));
        } else {
          console.log(chalk.blue(`🔍 [DRY RUN] 运行时错误修复预览: ${fileName}`));
        }

        this.fixStats.filesModified++;
        this.fixStats.errorsFixed++;

        return {
          success: true,
          fixedContent: fixedContent,
          newContent: fixedContent
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ 修复失败: ${fixResult.error || '未知错误'}`));
        }
        return fixResult;
      }

    } catch (error) {
      console.error(chalk.red(`运行时错误修复失败: ${error.message}`));
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 修复运行时错误文件
   */
  async fixRuntimeErrorFile(fileName, fileContent, errorContext) {
    try {
      const context = {
        taskType: 'runtime-error-fix',
        errorType: errorContext.type || 'runtime',
        fileName,
        message: errorContext.message,
        stack: errorContext.stack,
        componentTrace: errorContext.componentTrace
      };

      const prompt = this.promptBuilder.buildRuntimeErrorFixPrompt(
        fileName, fileContent, errorContext, context
      );

      const response = await this.callAI(prompt, {
        context: {
          taskType: 'runtime-error-fix',
          phase: 'fix-generation',
          fileName: fileName,
          errorType: errorContext.type
        }
      });

      return this.parseFixResponse(response, fileContent);
    } catch (error) {
      return {
        success: false,
        error: error.message,
        aiResponse: error.response || null
      };
    }
  }

  /**
   * 包装的 AI 调用方法，集成历史管理器
   * @private
   */
  async callAIWithHistory(prompt, options = {}, attemptId = null) {
    const startTime = Date.now();
    const context = options.context || {};

    try {
      const response = await this.callAI(prompt, options);
      const duration = Date.now() - startTime;

      // 记录成功的 AI 调用到历史管理器
      if (attemptId) {
        this.historyManager.recordAICall(attemptId, {
          taskType: context.taskType || 'unknown',
          phase: context.phase || 'unknown',
          prompt: prompt.substring(0, 500), // 只记录前500字符
          response: response.substring(0, 500), // 只记录前500字符
          success: true,
          duration,
          strategy: this._inferStrategyFromContext(context)
        });
      }

      return response;
    } catch (error) {
      const duration = Date.now() - startTime;

      // 记录失败的 AI 调用到历史管理器
      if (attemptId) {
        this.historyManager.recordAICall(attemptId, {
          taskType: context.taskType || 'unknown',
          phase: context.phase || 'unknown',
          prompt: prompt.substring(0, 500),
          response: '',
          success: false,
          error: error.message,
          duration,
          strategy: this._inferStrategyFromContext(context)
        });
      }

      throw error;
    }
  }

  /**
   * 从上下文推断修复策略
   * @private
   */
  _inferStrategyFromContext(context) {
    const { taskType, phase, fileName } = context;

    if (taskType === 'str-replace') {
      return 'string-replace-fix';
    }

    if (taskType === 'file-fix') {
      const ext = path.extname(fileName || '');
      if (ext === '.vue') {
        return 'vue-component-fix';
      } else if (ext === '.js' || ext === '.ts') {
        return 'javascript-syntax-fix';
      }
      return 'general-file-fix';
    }

    if (taskType === 'tool-call-generation') {
      return 'tool-call-generation';
    }

    return 'unknown-strategy';
  }

  async generateToolCalls(filePath, buildOutput, attemptNumber = 1, previousAttempts = [], suggestions = []) {
    try {
      const context = {
        attemptNumber,
        previousAttempts,
        taskType: 'tool-call-generation',
        suggestions
      };

      const prompt = this.promptBuilder.buildToolCallPrompt(filePath, buildOutput, context);
      const response = await this.callAI(prompt, {
        context: {
          taskType: 'tool-call-generation',
          attemptNumber: attemptNumber,
          phase: 'tool-calls',
          fileName: filePath
        }
      });

      return this.responseHandler.parseToolCallsResponse(response);
    } catch (error) {
      console.log(chalk.yellow(`⚠️  生成工具调用失败: ${error.message}`));
      return [];
    }
  }

  async executeToolCalls(toolCalls) {
    try {
      const result = await this.toolExecutor.executeToolCalls(toolCalls);

      if (result.success) {
        return this.toolExecutor.formatContextFiles(result.results);
      } else {
        console.log(chalk.yellow(`⚠️  工具调用执行失败: ${result.errors?.join(', ')}`));
        return {};
      }
    } catch (error) {
      console.log(chalk.yellow(`⚠️  工具调用执行异常: ${error.message}`));
      return {};
    }
  }

  /**
   * 第二轮AI调用：基于收集的文件内容生成修复代码
   */
  async generateFileFix(filePath, fileContent, buildOutput, contextFiles, attemptNumber = 1, fileIndex = 1, totalFiles = 1, previousAttempts = [], suggestions = [], attemptId = null) {
    try {
      this.currentFilePath = filePath;

      const context = {
        attemptNumber,
        fileIndex,
        totalFiles,
        previousAttempts,
        taskType: 'file-fix',
        suggestions
      };

      const shouldUseStrReplace = this.shouldUseStringReplace(fileContent, filePath);
      if (shouldUseStrReplace) {
        if (this.options.verbose) {
          console.log(chalk.gray('    🔧 文件较大，使用 str_replace 工具进行精确修复'));
        }

        if (!suggestions) {
          suggestions = await this.errorAnalyzer.generateErrorSuggestions(buildOutput);
        }

        const prompt = this.stringReplaceTool.generatePrompt(
          filePath, fileContent, buildOutput, contextFiles, context, suggestions
        );

        const response = await this.callAIWithHistory(prompt, {
          context: {
            taskType: 'str-replace',
            attemptNumber,
            phase: 'str-replace-generation',
            fileName: filePath
          }
        }, attemptId);

        const parseResult = this.stringReplaceTool.parseResponse(response, fileContent);

        if (parseResult.success) {
          const result = await this.executeStringReplace(
            parseResult.filePath || filePath,
            parseResult.oldString,
            parseResult.newString,
            parseResult.expectedReplacements
          );

          this.currentFilePath = null;
          return result;
        } else {
          this.currentFilePath = null;
          return parseResult;
        }
      } else {
        if (!suggestions) {
          suggestions = await this.errorAnalyzer.generateErrorSuggestions(buildOutput);
        }

        const prompt = this.promptBuilder.buildFileFixPromptWithContext(
          filePath, fileContent, buildOutput, contextFiles, context, suggestions
        );

        const response = await this.callAIWithHistory(prompt, {
          context: {
            taskType: 'file-fix',
            attemptNumber,
            phase: 'fix-generation',
            fileName: filePath
          }
        }, attemptId);

        const result = this.parseFixResponse(response, fileContent);
        this.currentFilePath = null;
        return result;
      }
    } catch (error) {
      this.currentFilePath = null;

      return {
        success: false,
        error: error.message,
        aiResponse: null
      };
    }
  }

  /**
   * 解析修复响应
   */
  parseFixResponse(response, originalContent) {
    try {
      if (response.startsWith('<template>')) {
        return {
          success: true,
          newContent: response,
          fixedContent: response,
          format: 'vue',
          metadata: {}
        };
      }

      const parseResult = this.responseHandler.parseResponse(response);
      if (parseResult.success) {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ✅ 解析成功，格式: ${parseResult.format}`));
          console.log(chalk.gray(`       内容长度: ${parseResult.content.length} 字符`));
        }

        const validationResult = this.validateFixedContent(parseResult.content, originalContent, this.currentFilePath || '');
        if (validationResult) {
          return {
            success: true,
            newContent: validationResult,
            fixedContent: validationResult,
            format: parseResult.format,
            metadata: parseResult.metadata
          };
        }
      }

      if (this.options.verbose) {
        console.log(chalk.gray(`       ❌ 使用PromptResponseHandler解析失败，尝试回退方案...`));
      }

      const extractedXml = this.responseHandler.extractFromCodeBlock(response);
      if (extractedXml.startsWith("<template>")) {
        return {
          success: true,
          aiResponse: extractedXml
        };
      }

      return this.parseFixResponseLegacy(extractedXml, originalContent);
    } catch (error) {
      console.warn(chalk.yellow('⚠️  解析 AI 修复响应失败'));
      if (this.options.verbose) {
        console.log(chalk.gray(`       错误详情: ${error.message}`));
      }
      return {
        success: false,
        error: `解析修复响应失败: ${error.message}`,
        aiResponse: response
      };
    }
  }

  /**
   * 旧版解析修复响应（向后兼容）
   */
  parseFixResponseLegacy(response, originalContent) {
    try {
      // 尝试解析运行时错误修复的特殊格式（优先级最高）
      const runtimeFixMatch = response.match(/<fix_result>\s*<fixed_content>([\s\S]*?)<\/fixed_content>\s*<changes_made>[\s\S]*?<\/changes_made>\s*<\/fix_result>/);

      if (runtimeFixMatch) {
        let content = runtimeFixMatch[1].trim();
        content = this.decodeHtmlEntities(content);

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent, this.currentFilePath || '');
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      }

      // 尝试解析 XML 格式
      const xmlMatch = response.match(/<fix_result>[\s\S]*?<fixed_content>([\s\S]*?)<\/fixed_content>[\s\S]*?<\/fix_result>/);

      if (xmlMatch) {
        let content = xmlMatch[1].trim();
        content = this.decodeHtmlEntities(content);

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent, this.currentFilePath || '');
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          }
        }
      }

      // 回退：尝试解析代码块格式
      const codeBlockMatch = response.match(/```(?:vue|js|ts|javascript|typescript|xml)?\s*([\s\S]*?)\s*```/);

      if (codeBlockMatch) {
        let content = codeBlockMatch[1].trim();

        if (content.startsWith('<template>')) {
          return {
            success: true,
            aiResponse: content
          }
        }

        // 检查代码块内容是否包含运行时修复格式
        const innerRuntimeFixMatch = content.match(/<fix_result>\s*<fixed_content>([\s\S]*?)<\/fixed_content>\s*<changes_made>[\s\S]*?<\/changes_made>\s*<\/fix_result>/);

        if (innerRuntimeFixMatch) {
          content = innerRuntimeFixMatch[1].trim();
          content = this.decodeHtmlEntities(content);
        }

        if (content) {
          const validationResult = this.validateFixedContent(content, originalContent, this.currentFilePath || '');
          if (validationResult) {
            return {
              success: true,
              newContent: validationResult,
              fixedContent: validationResult
            };
          } else {
            console.log(chalk.yellow(`       ❌ 内容校验失败，内容与原文件相同或不符合规范`));
          }
        }
      }

      return {
        success: false,
        error: '无法解析AI响应格式 <BuildFix - Legacy>',
        aiResponse: response
      };
    } catch (error) {
      return {
        success: false,
        error: `解析修复响应失败: ${error.message}`,
        aiResponse: response
      };
    }
  }

  /**
   * 解码 HTML 实体
   */
  decodeHtmlEntities(text) {
    const entities = {
      '&lt;': '<',
      '&gt;': '>',
      '&amp;': '&',
      '&quot;': '"',
      '&#39;': "'",
      '&apos;': "'"
    };

    return text.replace(/&[a-zA-Z0-9#]+;/g, (entity) => {
      return entities[entity] || entity;
    });
  }

  /**
   * 验证修复后的内容
   */
  validateFixedContent(fixedContent, originalContent, filePath = '') {
    if (!fixedContent) {
      if (this.options.verbose) {
        console.log(chalk.gray('       ❌ 修复内容为空'));
      }
      return null;
    }

    if (!filePath.endsWith(".vue")) {
      return fixedContent
    }

    const validator = new VueFileValidator({ verbose: this.options.verbose });
    const validationResult = validator.validateContent(fixedContent, originalContent, filePath);

    if (!validationResult.isValid) {
      if (this.options.verbose) {
        console.log(chalk.gray(`       ❌ 内容验证失败: ${validationResult.error}`));
      }
      return null;
    }

    if (this.options.verbose) {
      console.log(chalk.gray('       ✅ 内容验证通过'));
    }

    return validationResult.content;
  }

  /**
   * 获取之前的尝试记录
   */
  getPreviousAttempts(filesToFix, currentAttemptNumber) {
    const previousAttempts = [];

    // 从历史记录中获取之前的尝试
    for (let i = 1; i < currentAttemptNumber; i++) {
      const attemptKey = `attempt_${i}`;
      if (this.attemptHistory[attemptKey]) {
        this.attemptHistory[attemptKey].forEach(record => {
          if (filesToFix.includes(record.filePath)) {
            previousAttempts.push({
              attemptNumber: i,
              filePath: record.filePath,
              error: record.error,
              approach: record.approach || this.inferApproachFromError(record.error)
            });
          }
        });
      }
    }

    return previousAttempts;
  }

  /**
   * 记录失败的尝试
   */
  recordFailedAttempt(filePath, attemptNumber, error) {
    const attemptKey = `attempt_${attemptNumber}`;

    if (!this.attemptHistory[attemptKey]) {
      this.attemptHistory[attemptKey] = [];
    }

    this.attemptHistory[attemptKey].push({
      filePath,
      error,
      timestamp: new Date().toISOString(),
      approach: this.inferApproachFromError(error)
    });
  }

  /**
   * 从错误信息推断修复方法
   */
  inferApproachFromError(error) {
    if (!error || typeof error !== 'string') {
      return '未知方法';
    }

    const lowerError = error.toLowerCase();

    if (lowerError.includes('内容与原文件相同')) {
      return '小幅修改';
    } else if (lowerError.includes('缺少必要的文件结构')) {
      return '结构修复';
    } else if (lowerError.includes('内容过短')) {
      return '内容补充';
    } else if (lowerError.includes('语法错误') || lowerError.includes('syntax error')) {
      return '语法错误修复';
    } else if (lowerError.includes('导入') || lowerError.includes('import')) {
      return '导入语句修复';
    } else {
      return '通用修复';
    }
  }

  /**
   * 生成修复会话摘要
   */
  async generateSessionSummary() {
    try {
      const sessionId = new Date().toISOString().replace(/[:.]/g, '-').split('T')[0];
      const summaryPath = path.join(this.options.logDir, `session-summary-${sessionId}.json`);

      // 读取所有相关的日志文件
      const logFiles = await this.getSessionLogFiles();
      const sessionData = {
        sessionId: sessionId,
        projectPath: this.projectPath,
        startTime: new Date().toISOString(),
        endTime: new Date().toISOString(),
        totalAttempts: this.fixStats.attempts,
        filesAnalyzed: this.fixStats.filesAnalyzed,
        filesModified: this.fixStats.filesModified,
        attempts: []
      };

      // 按轮次组织日志数据
      for (const logFile of logFiles) {
        try {
          const logData = await fs.readJson(logFile);
          const attemptNumber = logData.context?.attemptNumber || 1;

          if (!sessionData.attempts[attemptNumber - 1]) {
            sessionData.attempts[attemptNumber - 1] = {
              attemptNumber: attemptNumber,
              phases: []
            };
          }

          sessionData.attempts[attemptNumber - 1].phases.push({
            phase: logData.context?.phase || 'unknown',
            taskType: logData.context?.taskType || 'unknown',
            fileName: logData.context?.fileName || 'unknown',
            success: logData.success,
            duration_ms: logData.duration_ms,
            timestamp: logData.timestamp,
            logFile: path.basename(logFile)
          });
        } catch (error) {
          console.warn(chalk.yellow(`⚠️  无法读取日志文件: ${logFile}`));
        }
      }

      // 写入会话摘要
      await fs.writeJson(summaryPath, sessionData, { spaces: 2 });
      console.log(chalk.blue(`📊 会话摘要已生成: ${path.basename(summaryPath)}`));

      return summaryPath;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  生成会话摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 获取会话相关的日志文件
   */
  async getSessionLogFiles() {
    try {
      // 确保日志目录存在
      await fs.ensureDir(this.options.logDir);

      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .map(file => path.join(this.options.logDir, file))
        .sort();

      if (this.options.verbose) {
        console.log(chalk.gray(`📁 找到 ${logFiles.length} 个 AI 调用日志文件`));

        // 显示最新的几个日志文件
        if (logFiles.length > 0) {
          const recentFiles = logFiles.slice(-3);
          console.log(chalk.gray('   最新日志文件:'));
          recentFiles.forEach(file => {
            console.log(chalk.gray(`   - ${path.basename(file)}`));
          });
        }
      }

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法读取日志目录 ${this.options.logDir}: ${error.message}`));

      // 尝试创建日志目录
      try {
        await fs.ensureDir(this.options.logDir);
        if (this.options.verbose) {
          console.log(chalk.green(`✅ 已创建日志目录: ${this.options.logDir}`));
        }
        return [];
      } catch (createError) {
        console.error(chalk.red(`❌ 无法创建日志目录: ${createError.message}`));
        return [];
      }
    }
  }

  /**
   * 获取修复统计信息
   */
  getFixStats() {
    const historyStats = this.historyManager.generateSessionSummary();
    return {
      ...this.fixStats,
      aiStats: this.getStats(),
      historyStats: historyStats
    };
  }

  /**
   * 保存会话历史摘要
   */
  async saveSessionHistory() {
    try {
      const summaryPath = await this.historyManager.saveSessionSummary();
      if (summaryPath && this.options.verbose) {
        console.log(chalk.blue(`📊 修复历史摘要已保存: ${path.basename(summaryPath)}`));
      }
      return summaryPath;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  保存修复历史摘要失败: ${error.message}`));
      return null;
    }
  }

  /**
   * 列出所有轮次的日志文件
   */
  async listSessionLogs() {
    try {
      const files = await fs.readdir(this.options.logDir);
      const logFiles = files
        .filter(file => file.endsWith('.json') && file.includes('ai-call'))
        .sort();

      if (logFiles.length === 0) {
        if (this.options.verbose) {
          console.log(chalk.gray('📝 没有找到 AI 调用日志文件'));
        }
        return [];
      }

      if (this.options.verbose) {
        console.log(chalk.blue(`📝 找到 ${logFiles.length} 个 AI 调用日志文件:`));
      }

      // 按轮次分组显示
      const attempts = {};
      for (const file of logFiles) {
        const match = file.match(/attempt(\d+)/);
        if (match) {
          const attemptNum = parseInt(match[1]);
          if (!attempts[attemptNum]) {
            attempts[attemptNum] = [];
          }
          attempts[attemptNum].push(file);
        }
      }

      // 显示每个轮次的日志
      Object.keys(attempts).sort((a, b) => parseInt(a) - parseInt(b)).forEach(attemptNum => {
        console.log(chalk.gray(`\n  轮次 ${attemptNum}:`));
        attempts[attemptNum].forEach(file => {
          console.log(chalk.gray(`    - ${file}`));
        });
      });

      return logFiles;
    } catch (error) {
      console.warn(chalk.yellow(`⚠️  无法列出日志文件: ${error.message}`));
      return [];
    }
  }

  /**
   * 记录本次尝试
   */
  recordAttempt(buildOutput, filesToFix) {
    const errorHash = this.errorAnalyzer.generateErrorHash(buildOutput);

    this.attemptHistory.errorHashes.push(errorHash);
    this.attemptHistory.filesToFix.push([...filesToFix]);
    this.attemptHistory.lastErrorOutput = buildOutput;

    // 只保留最近3次尝试的记录
    if (this.attemptHistory.errorHashes.length > 3) {
      this.attemptHistory.errorHashes.shift();
      this.attemptHistory.filesToFix.shift();
    }
  }

  /**
   * 判断是否应该使用字符串替换工具
   * 基于文件大小、行数等因素决定
   */
  shouldUseStringReplace(fileContent, filePath) {
    if (!fileContent || typeof fileContent !== 'string') {
      return false;
    }

    const lines = fileContent.split('\n').length;
    const characters = fileContent.length;

    const LINE_THRESHOLD = 500; // 超过 500 行
    const CHAR_THRESHOLD = 50000; // 超过50KB

    const exceedsLineThreshold = lines > LINE_THRESHOLD;
    const exceedsCharThreshold = characters > CHAR_THRESHOLD;

    if (this.options.verbose && (exceedsLineThreshold || exceedsCharThreshold)) {
      console.log(chalk.gray(`    📊 文件统计: ${lines} 行, ${characters} 字符`));
      console.log(chalk.gray(`       超过行数阈值 (${LINE_THRESHOLD}): ${exceedsLineThreshold}`));
      console.log(chalk.gray(`       超过字符阈值 (${CHAR_THRESHOLD}): ${exceedsCharThreshold}`));
    }

    return exceedsLineThreshold || exceedsCharThreshold;
  }

  /**
   * 执行字符串替换操作
   */
  async executeStringReplace(filePath, oldString, newString, expectedReplacements = 1) {
    try {
      // 添加调试信息
      if (this.options.verbose) {
        console.log(chalk.gray('       🔍 执行字符串替换调试信息:'));
        console.log(chalk.gray(`       文件路径: ${filePath}`));
        console.log(chalk.gray(`       old_string 长度: ${oldString.length} 字符`));
        console.log(chalk.gray(`       old_string 前50字符: ${JSON.stringify(oldString.substring(0, 50))}`));
        console.log(chalk.gray(`       期望替换次数: ${expectedReplacements}`));
      }

      const result = await this.toolExecutor.executeToolCall('str_replace', {
        file_path: filePath,
        old_string: oldString,
        new_string: newString,
        expected_replacements: expectedReplacements
      });

      if (result.success) {
        if (this.options.verbose) {
          console.log(chalk.gray('       ✅ str_replace 执行成功'));
        }

        return {
          success: true,
          fixedContent: null, // str_replace 不返回完整内容
          newContent: null, // str_replace 不返回完整内容
          toolResult: result,
          method: 'str_replace'
        };
      } else {
        if (this.options.verbose) {
          console.log(chalk.gray(`       ❌ str_replace 执行失败: ${result.error}`));

          // 添加更详细的调试信息
          if (result.error.includes('未找到要替换的文本')) {
            console.log(chalk.gray('       🔍 调试提示: 检查空格、缩进、换行符是否完全匹配'));
            console.log(chalk.gray('       📝 建议: 确保 old_string 包含足够的上下文以保证唯一匹配'));
          }
        }

        return {
          success: false,
          error: `str_replace 执行失败: ${result.error}`,
          toolResult: result
        };
      }
    } catch (error) {
      return {
        success: false,
        error: `str_replace 执行异常: ${error.message}`
      };
    }
  }

}

module.exports = BuildFixAgent;
