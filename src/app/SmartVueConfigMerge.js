const fs = require('fs-extra');
const path = require('path');
const chalk = require('chalk');
const { AIService } = require('../ai/AiService');

/**
 * SmartVueConfigMerge - 智能 Vue 配置合并工具
 *
 * 使用 AI 来智能合并 Vue 2 + Webpack 4 配置到 Vue 3 + Webpack 5
 * 分析老项目的 vue.config.js，结合新的模板配置，生成优化的配置文件
 */
class SmartVueConfigMerge {
  constructor(options = {}) {
    this.options = {
      verbose: options.verbose || false,
      dryRun: options.dryRun || false,
      ...options
    };

    this.aiService = new AIService({
      verbose: this.options.verbose,
      maxTokens: 6000,
      temperature: 0.1
    });
  }

  /**
   * 智能合并 Vue 配置文件
   * @param {string} projectPath - 项目路径（包含老的 vue.config.js）
   * @param {string} targetPath - 目标路径（生成新的 vue.config.js）
   * @returns {Promise<Object>} 合并结果
   */
  async mergeVueConfig(projectPath, targetPath) {
    try {
      // 1. 读取老的 vue.config.js
      const oldConfigPath = path.join(projectPath, 'vue.config.js');
      const oldConfigExists = await fs.pathExists(oldConfigPath);
      let oldConfigContent = '';

      if (oldConfigExists) {
        oldConfigContent = await fs.readFile(oldConfigPath, 'utf8');
      }

      // 2. 读取新的模板配置
      const templateConfigPath = path.join(__dirname, '../../config/vue.config.js');
      const templateConfigContent = await fs.readFile(templateConfigPath, 'utf8');

      // 3. 如果没有老配置，直接复制模板
      if (!oldConfigExists || !oldConfigContent.trim()) {
        if (!this.options.dryRun) {
          await fs.copy(templateConfigPath, path.join(targetPath, 'vue.config.js'));
        }

        return {
          success: true,
          action: 'template_copy',
          message: '未找到老的 vue.config.js，使用模板配置',
          aiUsed: false
        };
      }

      // 4. 使用 AI 智能合并配置
      if (!this.aiService.isEnabled()) {
        // AI 不可用时，回退到简单复制
        if (!this.options.dryRun) {
          await fs.copy(templateConfigPath, path.join(targetPath, 'vue.config.js'));
        }

        return {
          success: true,
          action: 'fallback_copy',
          message: 'AI 服务不可用，使用模板配置',
          aiUsed: false
        };
      }

      const mergedConfig = await this.generateMergedConfig(oldConfigContent, templateConfigContent);

      // 5. 写入合并后的配置
      const targetConfigPath = path.join(targetPath, 'vue.config.js');
      if (!this.options.dryRun) {
        await fs.writeFile(targetConfigPath, mergedConfig, 'utf8');
      }

      return {
        success: true,
        action: 'ai_merge',
        message: 'AI 智能合并配置完成',
        aiUsed: true,
        oldConfigPath,
        targetConfigPath,
        mergedConfig: this.options.verbose ? mergedConfig : undefined
      };

    } catch (error) {
      throw new Error(`Vue 配置合并失败: ${error.message}`);
    }
  }

  /**
   * 使用 AI 生成合并后的配置
   * @param {string} oldConfig - 老的配置内容
   * @param {string} templateConfig - 模板配置内容
   * @returns {Promise<string>} 合并后的配置内容
   */
  async generateMergedConfig(oldConfig, templateConfig) {
    const prompt = this.buildMergePrompt(oldConfig, templateConfig);

    let result = await this.aiService.callAI(prompt, {
      context: {
        taskType: 'vue-config-merge',
        phase: 'config-generation'
      }
    });

    // remove the trailing backticks from the AI response
    if (result.endsWith('```')) {
      result = result.slice(0, -3).trim(); // 去掉最后的 ```
    }

    return result;
  }

  /**
   * 构建 AI 提示词
   * @param {string} oldConfig - 老的配置内容
   * @param {string} templateConfig - 模板配置内容
   * @returns {string} AI 提示词
   */
  buildMergePrompt(oldConfig, templateConfig) {
    return `你是一个 Vue.js 迁移专家，需要将 Vue 2 + Webpack 4 的配置智能合并到 Vue 3 + Webpack 5 配置中。

## 任务目标
将老项目的 vue.config.js 配置与新的 Vue 3 + Webpack 5 模板配置进行智能合并，通过注释的方式保留有用的配置，升级过时的配置。

## 老项目配置 (Vue 2 + Webpack 4)
\`\`\`javascript
${oldConfig}
\`\`\`

## 新模板配置 (Vue 3 + Webpack 5)
\`\`\`javascript
${templateConfig}
\`\`\`

## 合并规则
- 将老的配置以注释的方式保留在新配置中
- 将老的配置以注释的方式保留在新配置中
- 保留老的 chainWebpack.config.externals

\`\`\`javascript`;
  }

  /**
   * 删除 babel.config.js（Vue 2 特有）
   * @param {string} targetPath - 目标路径
   * @returns {Promise<boolean>} 是否删除了文件
   */
  async removeBabelConfig(targetPath) {
    const babelConfigPath = path.join(targetPath, 'babel.config.js');

    if (await fs.pathExists(babelConfigPath)) {
      if (!this.options.dryRun) {
        await fs.remove(babelConfigPath);
      }
      return true;
    }

    return false;
  }

  /**
   * 获取 AI 服务统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return this.aiService.getStats();
  }
}

module.exports = SmartVueConfigMerge;
