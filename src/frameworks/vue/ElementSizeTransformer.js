const gogocode = require('gogocode');
const chalk = require('chalk');

/**
 * Element UI 到 Element Plus size 属性转换器
 * 主要处理 size="mini" 到 size="small" 的转换
 */
class ElementSizeTransformer {
  constructor(options = {}) {
    this.options = Object.assign({
      verbose: false
    }, options);
    
    this.stats = {
      success: 0,
      failed: 0,
      transformations: []
    };

    // Element Plus 支持的 size 值映射
    this.sizeMapping = {
      'mini': 'small',
      // 可以在这里添加其他需要转换的 size 值
    };
  }

  /**
   * 转换代码中的 Element size 属性
   * @param {string} code - 源代码
   * @param {string} filePath - 文件路径（用于调试）
   * @returns {string} 转换后的代码
   */
  async transform(code, filePath = '') {
    try {
      // 解析 Vue 文件
      const ast = gogocode(code, { parseOptions: { language: 'vue' } });
      const template = ast.find('<template></template>');
      
      if (!template.length) {
        return code; // 没有 template，直接返回
      }

      let hasChanges = false;
      const changes = [];

      // 查找所有 el- 开头的组件
      template.find('<$_$>').each((node) => {
        const tagName = node.attr('content.name');
        
        if (tagName && tagName.startsWith('el-')) {
          const attrs = node.attr('content.attributes') || [];
          
          attrs.forEach((attr) => {
            const key = attr.key?.content;
            const value = attr.value?.content;
            
            // 处理静态 size 属性：size="mini"
            if (key === 'size' && typeof value === 'string' && this.sizeMapping[value]) {
              const newValue = this.sizeMapping[value];
              attr.value.content = newValue;
              hasChanges = true;
              changes.push({
                type: 'size-attribute-transform',
                component: tagName,
                from: value,
                to: newValue,
                file: filePath
              });
              
              if (this.options.verbose) {
                console.log(chalk.gray(`  转换 ${tagName} size="${value}" 为 size="${newValue}"`));
              }
            }
            
            // 处理动态绑定的 size 属性：:size="'mini'"
            if (key === ':size' || (attr.key?.type === 'VDirectiveKey' && attr.key?.name?.name === 'bind' && attr.key?.argument?.content === 'size')) {
              const transformedValue = this.transformDynamicSizeValue(value);
              if (transformedValue !== value) {
                attr.value.content = transformedValue;
                hasChanges = true;
                changes.push({
                  type: 'dynamic-size-attribute-transform',
                  component: tagName,
                  from: value,
                  to: transformedValue,
                  file: filePath
                });
                
                if (this.options.verbose) {
                  console.log(chalk.gray(`  转换 ${tagName} :size="${value}" 为 :size="${transformedValue}"`));
                }
              }
            }
          });
        }
      });

      if (hasChanges) {
        this.stats.success++;
        this.stats.transformations.push(...changes);
        return ast.generate();
      }

      return code;
    } catch (error) {
      this.stats.failed++;
      if (this.options.verbose) {
        console.warn(chalk.yellow(`⚠️ Element size 转换失败: ${filePath} - ${error.message}`));
      }
      return code;
    }
  }

  /**
   * 转换动态绑定的 size 值
   * @param {string} value - 原始值
   * @returns {string} 转换后的值
   */
  transformDynamicSizeValue(value) {
    if (!value || typeof value !== 'string') {
      return value;
    }

    // 处理字符串字面量：'mini' -> 'small'
    for (const [oldSize, newSize] of Object.entries(this.sizeMapping)) {
      const patterns = [
        `'${oldSize}'`,
        `"${oldSize}"`,
        `\`${oldSize}\``
      ];
      
      for (const pattern of patterns) {
        if (value === pattern) {
          return value.replace(oldSize, newSize);
        }
      }
    }

    return value;
  }

  /**
   * 获取转换统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      ...this.stats,
      total: this.stats.success + this.stats.failed
    };
  }

  /**
   * 重置统计信息
   */
  resetStats() {
    this.stats = {
      success: 0,
      failed: 0,
      transformations: []
    };
  }
}

module.exports = ElementSizeTransformer;
