const fs = require('fs-extra');
const path = require('path');
const matter = require('gray-matter');

/**
 * 依赖映射器
 * 解析 migrator/docs 中的文档，提取 source -> target 依赖映射关系
 */
class ComponentDependencyMapper {
  constructor(docsPath) {
    this.docsPath = docsPath || path.join(__dirname, 'docs');
    this.mappings = new Map();
    this.loadMappings();
  }

  /**
   * 加载所有依赖映射
   */
  loadMappings() {
    try {
      if (!fs.existsSync(this.docsPath)) {
        console.warn(`依赖文档目录不存在: ${this.docsPath}`);
        return;
      }

      const files = fs.readdirSync(this.docsPath).filter(file => file.endsWith('.md'));

      for (const file of files) {
        const filePath = path.join(this.docsPath, file);
        const content = fs.readFileSync(filePath, 'utf8');

        try {
          const { data } = matter(content);

          // 支持新的文档格式 (source_package, target_package) 和旧格式 (source, target)
          const sourcePackage = data.source_package || data.source;
          const targetPackage = data.target_package || data.target;

          if (sourcePackage && targetPackage) {
            // 处理 source 字段包含多个依赖名的情况（用逗号分隔）
            const sources = sourcePackage.split(',').map(s => s.trim());

            for (const source of sources) {
              if (source) {
                // 构建映射信息，包含新文档格式的所有字段
                const mappingInfo = {
                  target: targetPackage,
                  // 兼容旧格式的 link 字段和新格式的 source/target 对象
                  link: data.link || (data.source && data.source.github) || (data.target && data.target.github),
                  docPath: filePath,
                  docContent: content,
                  // 新增字段
                  migrationType: data.migration_type,
                  difficulty: data.difficulty,
                  vue2Support: data.vue2_support,
                  vue3Support: data.vue3_support,
                  sourceInfo: data.source,
                  targetInfo: data.target,
                  installCommands: data.install_commands,
                  apiChanges: data.api_changes,
                  breakingChanges: data.breaking_changes,
                  vue2Example: data.vue2_example,
                  vue3Example: data.vue3_example,
                  complexityFactors: data.complexity_factors,
                  compatibility: data.compatibility
                };

                this.mappings.set(source, mappingInfo);
              }
            }
          }
        } catch (error) {
          console.warn(`解析文档失败: ${file}`, error.message);
        }
      }
    } catch (error) {
      console.error('加载依赖映射失败:', error.message);
    }
  }

  /**
   * 获取源依赖对应的目标依赖
   */
  getTargetDependency(sourceDep) {
    return this.mappings.get(sourceDep);
  }

  /**
   * 检查是否有映射
   */
  hasMapping(sourceDep) {
    return this.mappings.has(sourceDep);
  }

  /**
   * 获取所有映射
   */
  getAllMappings() {
    return Array.from(this.mappings.entries()).map(([source, info]) => ({
      source,
      target: info.target,
      link: info.link,
      docPath: info.docPath,
      migrationType: info.migrationType,
      difficulty: info.difficulty,
      vue2Support: info.vue2Support,
      vue3Support: info.vue3Support,
      sourceInfo: info.sourceInfo,
      targetInfo: info.targetInfo,
      installCommands: info.installCommands,
      apiChanges: info.apiChanges,
      breakingChanges: info.breakingChanges,
      complexityFactors: info.complexityFactors,
      compatibility: info.compatibility
    }));
  }

  /**
   * 获取需要迁移的依赖
   */
  getMigrationDependencies(packageJsonPath) {
    try {
      const packageJson = fs.readJsonSync(packageJsonPath);
      const allDeps = {
        ...packageJson.dependencies,
        ...packageJson.devDependencies
      };

      const migrationDeps = [];

      // 检查所有映射，看看哪些源依赖存在于 package.json 中
      for (const [source, mapping] of this.mappings.entries()) {
        if (allDeps[source]) {
          migrationDeps.push({
            source,
            sourceVersion: allDeps[source],
            target: mapping.target,
            link: mapping.link,
            docPath: mapping.docPath,
            // 新增字段
            migrationType: mapping.migrationType,
            difficulty: mapping.difficulty,
            vue2Support: mapping.vue2Support,
            vue3Support: mapping.vue3Support,
            sourceInfo: mapping.sourceInfo,
            targetInfo: mapping.targetInfo,
            installCommands: mapping.installCommands,
            apiChanges: mapping.apiChanges,
            breakingChanges: mapping.breakingChanges,
            vue2Example: mapping.vue2Example,
            vue3Example: mapping.vue3Example,
            complexityFactors: mapping.complexityFactors,
            compatibility: mapping.compatibility
          });
        }
      }

      return migrationDeps;
    } catch (error) {
      console.error('获取迁移依赖失败:', error.message);
      return [];
    }
  }

  /**
   * 更新 package.json 中的依赖
   */
  async updatePackageJsonDependencies(packageJsonPath, dryRun = false) {
    try {
      const packageJson = fs.readJsonSync(packageJsonPath);
      const migrationDeps = this.getMigrationDependencies(packageJsonPath);

      if (migrationDeps.length === 0) {
        console.log('📦 没有需要迁移的依赖');
        return { updated: 0, dependencies: [] };
      }

      let updated = 0;
      const updatedDeps = [];

      // 更新 dependencies
      if (packageJson.dependencies) {
        for (const migration of migrationDeps) {
          if (packageJson.dependencies[migration.source]) {
            const oldVersion = packageJson.dependencies[migration.source];
            delete packageJson.dependencies[migration.source];
            packageJson.dependencies[migration.target] = 'latest'; // 或者从文档中获取推荐版本

            updated++;
            updatedDeps.push({
              ...migration,
              oldVersion,
              newVersion: 'latest',
              section: 'dependencies'
            });
          }
        }
      }

      // 更新 devDependencies
      if (packageJson.devDependencies) {
        for (const migration of migrationDeps) {
          if (packageJson.devDependencies[migration.source]) {
            const oldVersion = packageJson.devDependencies[migration.source];
            delete packageJson.devDependencies[migration.source];
            packageJson.devDependencies[migration.target] = 'latest';

            updated++;
            updatedDeps.push({
              ...migration,
              oldVersion,
              newVersion: 'latest',
              section: 'devDependencies'
            });
          }
        }
      }

      if (!dryRun && updated > 0) {
        // 备份原文件
        // const backupPath = packageJsonPath + '.backup';
        // if (!fs.existsSync(backupPath)) {
        //   fs.copySync(packageJsonPath, backupPath);
        // }

        // 写入更新后的 package.json
        fs.writeJsonSync(packageJsonPath, packageJson, { spaces: 2 });
      }

      return {
        updated,
        dependencies: updatedDeps,
        packageJson: dryRun ? null : packageJson
      };

    } catch (error) {
      console.error('更新 package.json 失败:', error.message);
      throw error;
    }
  }

  /**
   * 获取依赖的迁移文档内容
   */
  getMigrationDoc(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.docContent : null;
  }

  /**
   * 获取依赖的详细迁移信息
   */
  getMigrationInfo(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    if (!mapping) return null;

    return {
      source: sourceDep,
      target: mapping.target,
      migrationType: mapping.migrationType,
      difficulty: mapping.difficulty,
      vue2Support: mapping.vue2Support,
      vue3Support: mapping.vue3Support,
      sourceInfo: mapping.sourceInfo,
      targetInfo: mapping.targetInfo,
      installCommands: mapping.installCommands,
      apiChanges: mapping.apiChanges,
      breakingChanges: mapping.breakingChanges,
      vue2Example: mapping.vue2Example,
      vue3Example: mapping.vue3Example,
      complexityFactors: mapping.complexityFactors,
      compatibility: mapping.compatibility,
      link: mapping.link,
      docPath: mapping.docPath
    };
  }

  /**
   * 根据难度级别获取依赖映射
   */
  getMappingsByDifficulty(difficulty) {
    const mappings = [];
    for (const [source, info] of this.mappings.entries()) {
      if (info.difficulty === difficulty) {
        mappings.push({
          source,
          target: info.target,
          difficulty: info.difficulty,
          migrationType: info.migrationType,
          link: info.link
        });
      }
    }
    return mappings;
  }

  /**
   * 根据迁移类型获取依赖映射
   */
  getMappingsByType(migrationType) {
    const mappings = [];
    for (const [source, info] of this.mappings.entries()) {
      if (info.migrationType === migrationType) {
        mappings.push({
          source,
          target: info.target,
          difficulty: info.difficulty,
          migrationType: info.migrationType,
          link: info.link
        });
      }
    }
    return mappings;
  }

  /**
   * 获取安装命令
   */
  getInstallCommands(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.installCommands : null;
  }

  /**
   * 获取 API 变更信息
   */
  getApiChanges(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.apiChanges : null;
  }

  /**
   * 获取破坏性变更信息
   */
  getBreakingChanges(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.breakingChanges : null;
  }

  /**
   * 获取代码示例
   */
  getCodeExamples(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    if (!mapping) return null;

    return {
      vue2Example: mapping.vue2Example,
      vue3Example: mapping.vue3Example
    };
  }

  /**
   * 获取兼容性信息
   */
  getCompatibilityInfo(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.compatibility : null;
  }

  /**
   * 获取复杂度因子
   */
  getComplexityFactors(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.complexityFactors : null;
  }

  /**
   * 检查依赖是否支持 Vue 3
   */
  isVue3Compatible(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.vue3Support : false;
  }

  /**
   * 检查依赖是否仍支持 Vue 2
   */
  isVue2Compatible(sourceDep) {
    const mapping = this.mappings.get(sourceDep);
    return mapping ? mapping.vue2Support : false;
  }

  /**
   * 获取迁移统计信息
   */
  getMigrationStats() {
    const stats = {
      total: this.mappings.size,
      byDifficulty: {},
      byType: {},
      vue2Support: 0,
      vue3Support: 0
    };

    for (const [, info] of this.mappings.entries()) {
      // 按难度统计
      if (info.difficulty) {
        stats.byDifficulty[info.difficulty] = (stats.byDifficulty[info.difficulty] || 0) + 1;
      }

      // 按类型统计
      if (info.migrationType) {
        stats.byType[info.migrationType] = (stats.byType[info.migrationType] || 0) + 1;
      }

      // Vue 版本支持统计
      if (info.vue2Support) stats.vue2Support++;
      if (info.vue3Support) stats.vue3Support++;
    }

    return stats;
  }
}

module.exports = ComponentDependencyMapper;
