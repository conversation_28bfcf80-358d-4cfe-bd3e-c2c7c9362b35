---
# Migration Configuration
source_package: vue2-org-tree
target_package: vue3-tree-org
migration_type: package_replacement
difficulty: medium
vue2_support: false
vue3_support: true

# Package Information
source:
  name: vue2-org-tree
  github: https://github.com/hukaibaihu/vue-org-tree
  npm: https://www.npmjs.com/package/vue2-org-tree
  description: "Vue 2 organization tree component"

target:
  name: vue3-tree-org
  github: https://github.com/sangtian152/vue3-tree-org
  npm: https://www.npmjs.com/package/vue3-tree-org
  description: "Vue 3 organization tree component"
  website: https://sangtian152.github.io/vue3-tree-org/

# Migration Steps
install_commands:
  remove: npm uninstall vue2-org-tree
  add: npm install vue3-tree-org

# Code Examples
vue2_example: |
  <template>
    <vue2-org-tree
      :data="treeData"
      :props="defaultProps"
      @on-node-click="onNodeClick"
    />
  </template>

  <script>
  import Vue2OrgTree from 'vue2-org-tree'

  export default {
    components: { Vue2OrgTree },
    data() {
      return {
        treeData: {
          id: 1,
          label: 'CEO',
          children: [
            { id: 2, label: 'Manager 1' },
            { id: 3, label: 'Manager 2' }
          ]
        },
        defaultProps: {
          children: 'children',
          label: 'label'
        }
      }
    },
    methods: {
      onNodeClick(node) {
        console.log('Node clicked:', node)
      }
    }
  }
  </script>

vue3_example: |
  <template>
    <vue3-tree-org
      :data="treeData"
      :props="defaultProps"
      @node-click="onNodeClick"
    />
  </template>

  <script setup>
  import { ref } from 'vue'
  import Vue3TreeOrg from 'vue3-tree-org'
  import 'vue3-tree-org/lib/vue3-tree-org.css'

  const treeData = ref({
    id: 1,
    label: 'CEO',
    children: [
      { id: 2, label: 'Manager 1' },
      { id: 3, label: 'Manager 2' }
    ]
  })

  const defaultProps = {
    children: 'children',
    label: 'label'
  }

  const onNodeClick = (node) => {
    console.log('Node clicked:', node)
  }
  </script>

# API Changes
api_changes:
  - type: component_name
    from: "<vue2-org-tree>"
    to: "<vue3-tree-org>"
    description: "Component name changed"
  - type: event_name
    from: "@on-node-click"
    to: "@node-click"
    description: "Event name simplified"
  - type: css_import
    from: "Automatic CSS inclusion"
    to: "Manual CSS import required"
    description: "Must import CSS manually"
  - type: plugin_registration
    from: "Vue.use(Vue2OrgTree)"
    to: "app.use(Vue3TreeOrg)"
    description: "Plugin registration updated for Vue 3"

# Breaking Changes
breaking_changes:
  - Component name changed from vue2-org-tree to vue3-tree-org
  - Event names simplified (on-node-click → node-click)
  - CSS must be imported manually
  - Plugin registration syntax changed
  - Different package name and structure

# Migration Complexity
complexity_factors:
  - Component API differences
  - Event name changes
  - CSS import requirements
  - Documentation primarily in Chinese
  - Different package structure
---

# 迁移指南: `vue2-tree-org` 到 `vue3-tree-org`

本指南旨在帮助你从 `vue2-org-tree` 迁移到其指定的 Vue 3 替代品 `vue3-tree-org`。这两个包都用于渲染组织结构图，但它们是为不同主要版本的 Vue 构建的。

迁移涉及更新包、更改注册方法以及适应新组件的 API。

**重要提示:** `vue3-tree-org` 的文档主要是中文，并托管在一个独立的网站上。本指南基于项目 README 中可用的信息。你可能需要参考[官方 `vue3-tree-org` 文档](https://sangtian152.github.io/vue3-tree-org/)以获取有关 props、事件和插槽的详细 API 细节。

## 主要变化

1.  **NPM 包**: 你将从 `vue2-org-tree` 包切换到 `vue3-tree-org`。
2.  **插件注册**: 初始化从 `Vue.use()` 更改为 `app.use()`。
3.  **CSS 导入**: `vue3-tree-org` 要求你手动导入其样式表。
4.  **组件 API**: 两个版本之间的 props、事件和插槽用法已发生变化。

---

## 分步迁移指南

### 1. 更新依赖

首先，卸载 Vue 2 的包并安装 Vue 3 的包。

```bash
npm uninstall vue2-org-tree
npm install vue3-tree-org
```

### 2. 更新插件注册

在你的应用程序入口文件 (`src/main.js` 或 `src/main.ts`) 中，更新插件的注册方式并导入所需的 CSS。

#### Vue 2 示例 (`main.js`)
```javascript
import Vue from 'vue';
import Vue2OrgTree from 'vue2-org-tree';

Vue.use(Vue2OrgTree);
```

#### Vue 3 示例 (`main.js`)
```javascript
import { createApp } from 'vue';
import App from './App.vue';
import vue3TreeOrg from 'vue3-tree-org';
// 不要忘记导入 CSS
import "vue3-tree-org/lib/vue3-tree-org.css";

const app = createApp(App);
app.use(vue3TreeOrg);
app.mount('#app');
```

### 3. 更新组件用法

你需要在模板中替换组件标签，并更新 props 和事件监听器。新组件名为 `vue3-tree-org`。

#### Vue 2 示例
```vue
<template>
  <vue2-org-tree
    :data="treeData"
    :props="fieldMappings"
    :collapsable="true"
    @on-node-click="handleNodeClick"
  />
</template>

<script>
export default {
  data() {
    return {
      treeData: { /* ... */ },
      fieldMappings: {
        label: 'name',
        children: 'children'
      }
    };
  },
  methods: {
    handleNodeClick(e, data) {
      console.log('节点被点击:', data.name);
    }
  }
};
</script>
```

#### Vue 3 示例
`vue3-tree-org` 的 API 发生了显著变化。虽然 `data` prop 仍然存在，但你映射字段、处理事件和自定义节点的方式已更新。新库还增加了许多功能，如拖放和上下文菜单。

```vue
<template>
  <vue3-tree-org
    :data="treeData"
    center
    :props="fieldMappings"
    @on-node-click="handleNodeClick"
  />
</template>

<script setup>
import { ref } from 'vue';

const treeData = ref({ /* ... */ });

// 字段映射的 prop 名称和结构可能不同。
// 请查阅官方文档。
const fieldMappings = {
  id: 'id',
  pid: 'pid',
  label: 'name',
  children: 'children'
};

const handleNodeClick = (e, data) => {
  console.log('节点被点击:', data.name);
};
</script>
```

### 4. 查看 API 差异

你必须仔细查阅 `vue3-tree-org` 文档以了解以下内容：

-   **Props**: 检查新 props 的功能，如水平/垂直布局 (`center`, `direction`)、缩放 (`zoom`) 和字段映射 (`props` 或 `fieldNames`)。
-   **事件**: 事件名称可能已更改（例如，`on-node-click` 可能现在是 `onNodeClick` 或其他名称）。
-   **插槽**: 新库提供了用于自定义节点内容的插槽 (`slot-expand`, `slot-node-card`)，这将不同于 Vue 2 版本中的 `renderContent`。

## 总结

从 `vue2-tree-org` 迁移到 `vue3-tree-org` 需要完全替换组件，包括更新包、注册、CSS 导入，以及适应一套新的 props、事件和插槽。由于英文 README 中的信息有限，**参考官方文档对于成功完成迁移至关重要**。 