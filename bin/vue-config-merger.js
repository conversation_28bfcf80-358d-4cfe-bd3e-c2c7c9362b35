#!/usr/bin/env node

const path = require('path');
const chalk = require('chalk');
const SmartVueConfigMerge = require('../src/app/SmartVueConfigMerge');

// 解析命令行参数
const argv = {
  project: process.argv[2],
  target: process.argv[3],
  verbose: process.argv.includes('--verbose') || process.argv.includes('-v'),
  dryRun: process.argv.includes('--dry-run') || process.argv.includes('-d')
};

// 显示帮助信息
if (process.argv.includes('--help') || process.argv.includes('-h') || !argv.project) {
  console.log(`
使用方法: node vue-config-merger.js <project-path> [target-path] [options]

参数:
  project-path            源项目路径（包含 vue.config.js）
  target-path             目标路径（生成新的 vue.config.js），默认与源路径相同

选项:
  --verbose, -v           显示详细日志
  --dry-run, -d           仅模拟运行，不实际修改文件
  --help, -h              显示帮助信息
  `);
  process.exit(argv.project ? 0 : 1);
}

// 主函数
async function main() {
  console.log(chalk.blue('🚀 Vue 配置智能合并工具'));
  
  try {
    // 解析路径
    const projectPath = path.resolve(argv.project);
    const targetPath = argv.target ? path.resolve(argv.target) : projectPath;
    
    console.log(chalk.gray(`📂 源项目路径: ${projectPath}`));
    console.log(chalk.gray(`📂 目标路径: ${targetPath}`));
    
    if (argv.dryRun) {
      console.log(chalk.yellow('⚠️  模拟运行模式，不会实际修改文件'));
    }
    
    // 初始化合并器
    const merger = new SmartVueConfigMerge({
      verbose: argv.verbose,
      dryRun: argv.dryRun
    });
    
    // 执行合并
    console.log(chalk.blue('🔄 开始智能合并 vue.config.js...'));
    const result = await merger.mergeVueConfig(projectPath, targetPath);
    
    // 输出结果
    if (result.success) {
      console.log(chalk.green('✅ 配置合并成功!'));
      console.log(chalk.gray(`   操作类型: ${result.action}`));
      console.log(chalk.gray(`   AI 辅助: ${result.aiUsed ? '是' : '否'}`));
      
      if (result.aiUsed && argv.verbose) {
        console.log(chalk.gray('📝 合并后的配置:'));
        console.log(result.mergedConfig);
      }
    } else {
      console.log(chalk.red('❌ 配置合并失败'));
    }
    
    // 显示 AI 统计
    if (result.aiUsed) {
      const stats = merger.getStats();
      console.log(chalk.blue('📊 AI 调用统计:'));
      console.log(chalk.gray(`   成功: ${stats.success}`));
      console.log(chalk.gray(`   失败: ${stats.failed}`));
    }
    
  } catch (error) {
    console.error(chalk.red(`❌ 错误: ${error.message}`));
    process.exit(1);
  }
}

// 执行主函数
main().catch(error => {
  console.error(chalk.red(`❌ 未处理的错误: ${error.message}`));
  console.error(error.stack);
  process.exit(1);
});
