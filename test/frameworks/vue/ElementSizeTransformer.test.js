const ElementSizeTransformer = require('../../../src/frameworks/vue/ElementSizeTransformer');

describe('ElementSizeTransformer', () => {
  let transformer;

  beforeEach(() => {
    transformer = new ElementSizeTransformer({ verbose: false });
  });

  describe('基本转换功能', () => {
    it('应该转换 el-table 的 size="mini" 为 size="small"', async () => {
      const input = `
        <template>
          <el-table
            ref="tableMain"
            :data="table_data._data_list_"
            border
            max-height="500"
            stripe
            style="width: 100%"
            size="mini"
          >
          </el-table>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('size="small"');
      expect(result).not.toContain('size="mini"');
    });

    it('应该转换 el-button 的 size="mini" 为 size="small"', async () => {
      const input = `
        <template>
          <el-button type="primary" size="mini">按钮</el-button>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('size="small"');
      expect(result).not.toContain('size="mini"');
    });

    it('应该转换多个组件的 size 属性', async () => {
      const input = `
        <template>
          <div>
            <el-button size="mini">按钮1</el-button>
            <el-input size="mini" placeholder="输入"></el-input>
            <el-select size="mini">
              <el-option label="选项1" value="1"></el-option>
            </el-select>
          </div>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('el-button size="small"');
      expect(result).toContain('el-input size="small"');
      expect(result).toContain('el-select size="small"');
      expect(result).not.toContain('size="mini"');
    });
  });

  describe('动态绑定转换', () => {
    it('应该转换 :size="\'mini\'" 为 :size="\'small\'"', async () => {
      const input = `
        <template>
          <el-button :size="'mini'">按钮</el-button>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain(':size="\'small\'"');
      expect(result).not.toContain(':size="\'mini\'"');
    });

    it('应该转换 :size=""mini"" 为 :size=""small""', async () => {
      const input = `
        <template>
          <el-button :size='"mini"'>按钮</el-button>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain(':size=\'"small"\'');
      expect(result).not.toContain(':size=\'"mini"\'');
    });

    it('不应该转换变量绑定 :size="sizeVar"', async () => {
      const input = `
        <template>
          <el-button :size="sizeVar">按钮</el-button>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain(':size="sizeVar"');
      expect(result).toBe(input); // 应该没有变化
    });
  });

  describe('边界情况', () => {
    it('不应该转换非 el- 组件的 size 属性', async () => {
      const input = `
        <template>
          <div size="mini">
            <custom-component size="mini"></custom-component>
          </div>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toBe(input); // 应该没有变化
    });

    it('不应该转换已经是 small 的 size 属性', async () => {
      const input = `
        <template>
          <el-button size="small">按钮</el-button>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toBe(input); // 应该没有变化
    });

    it('应该处理没有 template 的文件', async () => {
      const input = `
        <script>
        export default {
          name: 'Test'
        }
        </script>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toBe(input); // 应该没有变化
    });

    it('应该处理嵌套组件', async () => {
      const input = `
        <template>
          <el-form>
            <el-form-item>
              <el-button size="mini">提交</el-button>
            </el-form-item>
            <el-table size="mini">
              <el-table-column>
                <template #default="scope">
                  <el-button size="mini" @click="edit(scope.row)">编辑</el-button>
                </template>
              </el-table-column>
            </el-table>
          </el-form>
        </template>
      `;

      const result = await transformer.transform(input, 'test.vue');

      expect(result).toContain('el-button size="small"');
      expect(result).toContain('el-table size="small"');
      expect(result).not.toContain('size="mini"');
    });
  });

  describe('统计信息', () => {
    it('应该正确记录转换统计', async () => {
      const input = `
        <template>
          <el-button size="mini">按钮1</el-button>
          <el-input size="mini">输入</el-input>
        </template>
      `;

      await transformer.transform(input, 'test.vue');
      const stats = transformer.getStats();

      expect(stats.success).toBe(1);
      expect(stats.failed).toBe(0);
      expect(stats.transformations).toHaveLength(2);
      expect(stats.transformations[0].type).toBe('size-attribute-transform');
      expect(stats.transformations[0].from).toBe('mini');
      expect(stats.transformations[0].to).toBe('small');
    });

    it('应该能重置统计信息', async () => {
      const input = `
        <template>
          <el-button size="mini">按钮</el-button>
        </template>
      `;

      await transformer.transform(input, 'test.vue');
      transformer.resetStats();
      const stats = transformer.getStats();

      expect(stats.success).toBe(0);
      expect(stats.failed).toBe(0);
      expect(stats.transformations).toHaveLength(0);
    });
  });
});
