const VueStylePreserver = require('../../../src/frameworks/vue/VueStylePreserver');

describe('VueStylePreserver', () => {
    let preserver;

    beforeEach(() => {
        preserver = new VueStylePreserver({ verbose: false });
    });

    describe('extractStyleBlocks', () => {
        test('应该提取单个 style 块', () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<style scoped>
/* 这是一个注释 */
.hello {
  color: red;
}
</style>

<script>
export default {
  name: 'Hello'
}
</script>
            `.trim();

            const result = preserver.extractStyleBlocks(vueContent);

            expect(result.originalStyleBlocks).toHaveLength(1);
            expect(result.contentWithoutStyles).not.toContain('<style');
            expect(result.originalStyleBlocks[0].content).toContain('/* 这是一个注释 */');
            expect(result.originalStyleBlocks[0].content).toContain('.hello');
        });

        test('应该提取多个 style 块', () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<style>
/* 全局样式 */
.global {
  margin: 0;
}
</style>

<style scoped>
/* 局部样式 */
.scoped {
  padding: 10px;
}
</style>

<script>
export default {
  name: 'Hello'
}
</script>
            `.trim();

            const result = preserver.extractStyleBlocks(vueContent);

            expect(result.originalStyleBlocks).toHaveLength(2);
            expect(result.contentWithoutStyles).not.toContain('<style');
        });

        test('应该处理带有 lang="scss" 的 style 块', () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<style lang="scss" scoped>
/* SCSS 注释 */
$primary-color: #007bff;

.hello {
  color: $primary-color;

  &:hover {
    // 这是另一个注释
    color: darken($primary-color, 10%);
  }
}
</style>
            `.trim();

            const result = preserver.extractStyleBlocks(vueContent);

            expect(result.originalStyleBlocks).toHaveLength(1);
            expect(result.originalStyleBlocks[0].content).toContain('lang="scss"');
            expect(result.originalStyleBlocks[0].content).toContain('/* SCSS 注释 */');
            expect(result.originalStyleBlocks[0].content).toContain('// 这是另一个注释');
            expect(result.originalStyleBlocks[0].content).toContain('$primary-color');
        });

        test('应该处理没有 style 块的 Vue 文件', () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<script>
export default {
  name: 'Hello'
}
</script>
            `.trim();

            const result = preserver.extractStyleBlocks(vueContent);

            expect(result.originalStyleBlocks).toHaveLength(0);
            expect(result.contentWithoutStyles).toBe(vueContent);
        });
    });

    describe('restoreStyleBlocks', () => {
        test('应该恢复 style 块到正确位置', () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<style scoped>
/* 注释 */
.hello {
  color: red;
}
</style>

<script>
export default {
  name: 'Hello'
}
</script>
            `.trim();

            const { contentWithoutStyles, originalStyleBlocks } = preserver.extractStyleBlocks(vueContent);

            // 模拟转换过程，添加一个新的 style 块
            const transformedContent = contentWithoutStyles.replace('Hello', 'Hi') + '\n\n<style>\n.new { color: blue; }\n</style>';

            const restoredContent = preserver.restoreStyleBlocks(transformedContent, originalStyleBlocks);

            expect(restoredContent).toContain('<style scoped>');
            expect(restoredContent).toContain('/* 注释 */');
            expect(restoredContent).toContain('.hello');
            expect(restoredContent).toContain('Hi'); // 确保转换生效
        });
    });

    describe('processVueFile', () => {
        test('应该保护 style 块在转换过程中不被破坏', async () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<style lang="scss" scoped>
/* 重要的注释 */
$color: #ff0000;
.hello {
  color: $color;
  // 行内注释
}
</style>

<script>
export default {
  name: 'Hello'
}
</script>
            `.trim();

            // 模拟一个会破坏注释格式的转换函数
            const mockTransform = async (content) => {
                // 这个转换会移除所有注释（模拟 gogocode 的问题）
                return content.replace(/\/\*[\s\S]*?\*\//g, '')
                             .replace(/\/\/.*$/gm, '')
                             .replace('Hello', 'Hi');
            };

            const result = await preserver.processVueFile(vueContent, mockTransform);
            
            // 验证 style 块中的注释被保留
            expect(result).toContain('/* 重要的注释 */');
            expect(result).toContain('// 行内注释');
            expect(result).toContain('$color: #ff0000;');
            
            // 验证其他部分的转换生效
            expect(result).toContain('Hi'); // template 中的转换
            
            // 验证 template 和 script 部分的转换生效，但 style 块被保护
            expect(result).toContain('export default {'); // script 部分保持不变
        });

        test('应该处理没有 style 块的文件', async () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<script>
export default {
  name: 'Test'
}
</script>
            `.trim();

            const mockTransform = async (content) => {
                return content.replace('Hello', 'Hi');
            };

            const result = await preserver.processVueFile(vueContent, mockTransform);

            expect(result).toContain('Hi');
            expect(result).not.toContain('Hello');
        });

        test('应该在转换失败时回退到原始转换', async () => {
            const vueContent = `
<template>
  <div>Hello</div>
</template>

<style>
.hello { color: red; }
</style>
            `.trim();

            const mockTransform = async (content) => {
                return content.replace('Hello', 'Hi');
            };

            // 模拟 hyntax 解析失败的情况
            const originalTokenize = require('hyntax-yx').tokenize;
            require('hyntax-yx').tokenize = () => {
                throw new Error('Parse error');
            };

            const result = await preserver.processVueFile(vueContent, mockTransform);
            
            // 应该回退到原始转换
            expect(result).toContain('Hi');
            
            // 恢复原始函数
            require('hyntax-yx').tokenize = originalTokenize;
        });
    });

    describe('createPlaceholder', () => {

    });
});
