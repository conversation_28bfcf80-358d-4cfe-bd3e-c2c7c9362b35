const fs = require('fs-extra')
const path = require('path')
const os = require('os')

const SassMigrator = require('../../../src/app/SassMigrator')

describe('SassMigrator - Nested Imports', () => {
  let testProjectPath
  let migrator

  beforeEach(async () => {
    // 创建临时测试目录
    testProjectPath = await fs.mkdtemp(path.join(os.tmpdir(), 'sass-migrator-nested-test-'))
    migrator = new SassMigrator(testProjectPath, {
      verbose: true,
      preserveNestedImports: true // 默认保持嵌套导入原样
    })
  })

  afterEach(async () => {
    // 清理测试目录
    await fs.remove(testProjectPath)
  })

  describe('Nested Import Detection', () => {
    test('should detect @import statements nested within CSS rules', async () => {
      const testContent = `
@charset "utf-8";

.fm-blue-theme {
  @import "./theme-color/theme-blue.scss";
  @import "./button.scss";
  @import "./card.scss";
  @import "./color.scss";
  @import "./dialog.scss";
  @import "./pagination.scss";
  @import "./table.scss";
  @import "./tabs.scss";
  @import "./layout.scss";
  @import "./radio.scss";
}

.another-theme {
  color: blue;
  @import "./another-theme.scss";
}
`

      // 创建测试文件
      const testFile = path.join(testProjectPath, 'nested-imports.scss')
      await fs.writeFile(testFile, testContent)

      // 创建被导入的文件（避免路径解析错误）
      await fs.ensureDir(path.join(testProjectPath, 'theme-color'))
      await fs.writeFile(path.join(testProjectPath, 'theme-color', 'theme-blue.scss'), '$blue: #007bff;')
      await fs.writeFile(path.join(testProjectPath, 'button.scss'), '.button { color: red; }')
      await fs.writeFile(path.join(testProjectPath, 'card.scss'), '.card { padding: 1rem; }')
      await fs.writeFile(path.join(testProjectPath, 'color.scss'), '$primary: #333;')
      await fs.writeFile(path.join(testProjectPath, 'dialog.scss'), '.dialog { z-index: 1000; }')
      await fs.writeFile(path.join(testProjectPath, 'pagination.scss'), '.pagination { margin: 1rem; }')
      await fs.writeFile(path.join(testProjectPath, 'table.scss'), '.table { width: 100%; }')
      await fs.writeFile(path.join(testProjectPath, 'tabs.scss'), '.tabs { border: 1px solid; }')
      await fs.writeFile(path.join(testProjectPath, 'layout.scss'), '.layout { display: flex; }')
      await fs.writeFile(path.join(testProjectPath, 'radio.scss'), '.radio { margin: 0.5rem; }')
      await fs.writeFile(path.join(testProjectPath, 'another-theme.scss'), '.another { color: green; }')

      // 执行迁移
      const result = await migrator.migrateFile(testFile)

      // 验证结果
      expect(result.success).toBe(true)

      // 读取处理后的文件内容
      const processedContent = await fs.readFile(testFile, 'utf8')

      // 验证嵌套的@import被保持原样（因为@use不支持嵌套）
      expect(processedContent).toContain('@import "./theme-color/theme-blue.scss";')
      expect(processedContent).toContain('@import "./button.scss";')
      expect(processedContent).toContain('@import "./another-theme.scss";')

      // 验证CSS结构没有被破坏
      expect(processedContent).toContain('.fm-blue-theme {')
      expect(processedContent).toContain('.another-theme {')
    })

    test('should convert top-level @import to @use while preserving nested ones', async () => {
      const testContent = `
@import "./global-variables.scss";
@import "./global-mixins.scss";

.component {
  @import "./component-specific.scss";
  color: $primary-color;
}

@import "./utilities.scss";
`

      // 创建测试文件
      const testFile = path.join(testProjectPath, 'mixed-imports.scss')
      await fs.writeFile(testFile, testContent)

      // 创建被导入的文件
      await fs.writeFile(path.join(testProjectPath, 'global-variables.scss'), '$primary-color: #007bff;')
      await fs.writeFile(path.join(testProjectPath, 'global-mixins.scss'), '@mixin button { padding: 1rem; }')
      await fs.writeFile(path.join(testProjectPath, 'component-specific.scss'), '.specific { margin: 1rem; }')
      await fs.writeFile(path.join(testProjectPath, 'utilities.scss'), '.util { display: block; }')

      // 执行迁移
      const result = await migrator.migrateFile(testFile)

      // 验证结果
      expect(result.success).toBe(true)

      // 读取处理后的文件内容
      const processedContent = await fs.readFile(testFile, 'utf8')

      // 验证顶级@import被转换为@use（路径可能会被规范化）
      expect(processedContent).toContain("@use 'global-variables.scss' as *;")
      expect(processedContent).toContain("@use 'global-mixins.scss' as *;")
      expect(processedContent).toMatch(/@use ['"]utilities\.scss['"] as \w+;/)

      // 验证嵌套的@import保持原样
      expect(processedContent).toContain('@import "./component-specific.scss";')

      // 验证CSS结构没有被破坏
      expect(processedContent).toContain('.component {')
    })

    test('should handle complex nesting scenarios', async () => {
      const testContent = `
@media (max-width: 768px) {
  .responsive {
    @import "./mobile-styles.scss";
    
    &:hover {
      @import "./mobile-hover.scss";
    }
  }
}

@supports (display: grid) {
  .grid-container {
    @import "./grid-styles.scss";
  }
}
`

      // 创建测试文件
      const testFile = path.join(testProjectPath, 'complex-nesting.scss')
      await fs.writeFile(testFile, testContent)

      // 创建被导入的文件
      await fs.writeFile(path.join(testProjectPath, 'mobile-styles.scss'), '.mobile { font-size: 14px; }')
      await fs.writeFile(path.join(testProjectPath, 'mobile-hover.scss'), '.mobile-hover { opacity: 0.8; }')
      await fs.writeFile(path.join(testProjectPath, 'grid-styles.scss'), '.grid { display: grid; }')

      // 执行迁移
      const result = await migrator.migrateFile(testFile)

      // 验证结果
      expect(result.success).toBe(true)

      // 读取处理后的文件内容
      const processedContent = await fs.readFile(testFile, 'utf8')

      // 验证所有嵌套的@import都被保持原样
      expect(processedContent).toContain('@import "./mobile-styles.scss";')
      expect(processedContent).toContain('@import "./mobile-hover.scss";')
      expect(processedContent).toContain('@import "./grid-styles.scss";')

      // 验证CSS结构没有被破坏
      expect(processedContent).toContain('@media (max-width: 768px)')
      expect(processedContent).toContain('@supports (display: grid)')
    })
  })

  describe('Context Analysis', () => {
    test('should correctly analyze import context', () => {
      const content = `
.selector {
  color: red;
  @import "./test.scss";
}
`
      const importIndex = content.indexOf('@import')
      const context = migrator.analyzeImportContext(content, importIndex)

      expect(context.isNested).toBe(true)
      expect(context.braceLevel).toBe(1)
      expect(context.nearestSelector).toBe('.selector')
    })

    test('should handle multiple nesting levels', () => {
      const content = `
.outer {
  .inner {
    @import "./nested.scss";
  }
}
`
      const importIndex = content.indexOf('@import')
      const context = migrator.analyzeImportContext(content, importIndex)

      expect(context.isNested).toBe(true)
      expect(context.braceLevel).toBe(2)
    })

    test('should ignore braces in comments and strings', () => {
      const content = `
/* { this brace in comment should be ignored } */
.selector {
  content: "{ this brace in string should be ignored }";
  @import "./test.scss";
}
`
      const importIndex = content.indexOf('@import')
      const context = migrator.analyzeImportContext(content, importIndex)

      expect(context.isNested).toBe(true)
      expect(context.braceLevel).toBe(1)
    })
  })

  describe('Configuration Options', () => {
    test('should respect preserveNestedImports option', async () => {
      const migratorWithConversion = new SassMigrator(testProjectPath, {
        preserveNestedImports: false // 强制转换嵌套导入
      })

      const testContent = `
.theme {
  @import "./theme.scss";
}
`

      const testFile = path.join(testProjectPath, 'force-convert.scss')
      await fs.writeFile(testFile, testContent)
      await fs.writeFile(path.join(testProjectPath, 'theme.scss'), '.theme-content { color: blue; }')

      const result = await migratorWithConversion.migrateFile(testFile)
      expect(result.success).toBe(true)

      const processedContent = await fs.readFile(testFile, 'utf8')
      
      // 应该包含警告注释和转换后的@use（路径可能会被规范化）
      expect(processedContent).toContain('/* 警告: 原为嵌套@import，@use不支持嵌套使用 */')
      expect(processedContent).toMatch(/@use ['"]theme\.scss['"] as \w+;/)
    })
  })
})
