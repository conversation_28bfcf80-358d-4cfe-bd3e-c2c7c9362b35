const fs = require('fs-extra');
const path = require('path');
const PackageJsonMerger = require('../../../src/features/package-json/PackageJsonMerger');

describe('PackageJsonMerger - Conditional Dependencies', () => {
  let tempDir;
  let packageJsonMerger;

  beforeEach(async () => {
    // 创建临时目录
    tempDir = path.join(__dirname, 'temp-test-' + Date.now());
    await fs.ensureDir(tempDir);

    // 创建测试用的 package.json
    const testPackageJson = {
      name: 'test-project',
      version: '1.0.0',
      dependencies: {
        'crypto-js': '^4.1.1',
        'vue': '^3.3.0'
      },
      devDependencies: {
        '@vue/cli-service': '^5.0.8'
      }
    };

    const packageJsonPath = path.join(tempDir, 'package.json');
    await fs.writeJson(packageJsonPath, testPackageJson, { spaces: 2 });

    // 创建 PackageJsonMerger 实例（升级模式）
    packageJsonMerger = new PackageJsonMerger({
      projectPath: tempDir,
      verbose: true
    });
  });

  afterEach(async () => {
    // 清理临时目录
    if (await fs.pathExists(tempDir)) {
      await fs.remove(tempDir);
    }
  });

  describe('crypto-js 条件依赖测试', () => {
    test('当项目使用 crypto-js 时应该添加 crypto-browserify', async () => {
      // 执行升级
      const result = await packageJsonMerger.upgrade();

      // 验证结果
      expect(result.success).toBe(true);

      // 读取更新后的 package.json
      const packageJsonPath = path.join(tempDir, 'package.json');
      const updatedPackageJson = await fs.readJson(packageJsonPath);

      // 验证 crypto-browserify 被添加到 devDependencies
      expect(updatedPackageJson.devDependencies).toHaveProperty('crypto-browserify');
      expect(updatedPackageJson.devDependencies['crypto-browserify']).toBe('^3.12.0');

      // 验证添加记录
      const addedDeps = result.added.filter(dep => dep.name === 'crypto-browserify');
      expect(addedDeps).toHaveLength(1);
      expect(addedDeps[0].type).toBe('devDependencies');
      expect(addedDeps[0].version).toBe('^3.12.0');
    });

    test('当项目不使用 crypto-js 时不应该添加 crypto-browserify', async () => {
      // 修改 package.json，移除 crypto-js
      const packageJsonPath = path.join(tempDir, 'package.json');
      const packageJson = await fs.readJson(packageJsonPath);
      delete packageJson.dependencies['crypto-js'];
      await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });

      // 重新创建 PackageJsonMerger 实例
      packageJsonMerger = new PackageJsonMerger({
        projectPath: tempDir,
        verbose: true
      });

      // 执行升级
      const result = await packageJsonMerger.upgrade();

      // 验证结果
      expect(result.success).toBe(true);

      // 读取更新后的 package.json
      const updatedPackageJson = await fs.readJson(packageJsonPath);

      // 验证 crypto-browserify 没有被添加
      expect(updatedPackageJson.devDependencies).not.toHaveProperty('crypto-browserify');

      // 验证没有添加记录
      const addedDeps = result.added.filter(dep => dep.name === 'crypto-browserify');
      expect(addedDeps).toHaveLength(0);
    });

    test('当 crypto-browserify 已存在时不应该重复添加', async () => {
      // 修改 package.json，预先添加 crypto-browserify
      const packageJsonPath = path.join(tempDir, 'package.json');
      const packageJson = await fs.readJson(packageJsonPath);
      packageJson.devDependencies['crypto-browserify'] = '^3.11.0';
      await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });

      // 重新创建 PackageJsonMerger 实例
      packageJsonMerger = new PackageJsonMerger({
        projectPath: tempDir,
        verbose: true
      });

      // 执行升级
      const result = await packageJsonMerger.upgrade();

      // 验证结果
      expect(result.success).toBe(true);

      // 读取更新后的 package.json
      const updatedPackageJson = await fs.readJson(packageJsonPath);

      // 验证 crypto-browserify 版本保持不变（不被覆盖）
      expect(updatedPackageJson.devDependencies['crypto-browserify']).toBe('^3.11.0');

      // 验证没有添加记录
      const addedDeps = result.added.filter(dep => dep.name === 'crypto-browserify');
      expect(addedDeps).toHaveLength(0);
    });

    test('crypto-js 在 devDependencies 中时也应该触发条件', async () => {
      // 修改 package.json，将 crypto-js 移到 devDependencies
      const packageJsonPath = path.join(tempDir, 'package.json');
      const packageJson = await fs.readJson(packageJsonPath);
      delete packageJson.dependencies['crypto-js'];
      packageJson.devDependencies['crypto-js'] = '^4.1.1';
      await fs.writeJson(packageJsonPath, packageJson, { spaces: 2 });

      // 重新创建 PackageJsonMerger 实例
      packageJsonMerger = new PackageJsonMerger({
        projectPath: tempDir,
        verbose: true
      });

      // 执行升级
      const result = await packageJsonMerger.upgrade();

      // 验证结果
      expect(result.success).toBe(true);

      // 读取更新后的 package.json
      const updatedPackageJson = await fs.readJson(packageJsonPath);

      // 验证 crypto-browserify 被添加
      expect(updatedPackageJson.devDependencies).toHaveProperty('crypto-browserify');
      expect(updatedPackageJson.devDependencies['crypto-browserify']).toBe('^3.12.0');
    });
  });

  describe('条件依赖配置验证', () => {
    test('应该正确加载条件依赖配置', async () => {
      await packageJsonMerger.loadConfig();
      
      const conditionalConfig = packageJsonMerger.config?.migrationSettings?.conditionalDependencies?.rules;
      expect(conditionalConfig).toBeDefined();
      expect(conditionalConfig['crypto-browserify']).toBeDefined();
      expect(conditionalConfig['crypto-browserify'].condition.hasPackage).toBe('crypto-js');
      expect(conditionalConfig['crypto-browserify'].version).toBe('^3.12.0');
      expect(conditionalConfig['crypto-browserify'].section).toBe('devDependencies');
    });

    test('getConditionalDependencies 方法应该正确工作', async () => {
      await packageJsonMerger.loadConfig();
      
      const depsWithCryptoJs = {
        'crypto-js': '^4.1.1',
        'vue': '^3.3.0'
      };
      
      const conditionalDeps = packageJsonMerger.getConditionalDependencies(depsWithCryptoJs);
      expect(conditionalDeps).toHaveProperty('crypto-browserify');
      expect(conditionalDeps['crypto-browserify']).toBe('^3.12.0');
      
      const depsWithoutCryptoJs = {
        'vue': '^3.3.0'
      };
      
      const conditionalDepsEmpty = packageJsonMerger.getConditionalDependencies(depsWithoutCryptoJs);
      expect(conditionalDepsEmpty).not.toHaveProperty('crypto-browserify');
    });
  });
});
